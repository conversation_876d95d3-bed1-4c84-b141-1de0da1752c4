PODS:
  - aliyunpan_flutter_sdk_auth (0.0.1):
    - Flutter
  - apple_pay_plugin (0.0.1):
    - Flutter
  - audio_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - audio_session (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - ffmpeg_kit_flutter_new (1.0.0):
    - ffmpeg_kit_flutter_new/full-gpl-lts (= 1.0.0)
    - Flutter
  - ffmpeg_kit_flutter_new/full-gpl-lts (1.0.0):
    - Flutter
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_mxlogger (0.0.1):
    - Flutter
    - MXLogger (= 1.2.14)
  - flutter_plugin_stkouyu (0.0.1):
    - Flutter
  - flutter_sound (9.28.0):
    - Flutter
    - flutter_sound_core (= 9.28.0)
  - flutter_sound_core (9.28.0)
  - fluttertoast (0.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - media_kit_libs_ios_video (1.0.4):
    - Flutter
  - media_kit_video (0.0.1):
    - Flutter
  - MXLogger (1.2.14):
    - MXLoggerCore (= 1.2.14)
  - MXLoggerCore (1.2.14)
  - open_filex (0.0.2):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - receive_sharing_intent (1.8.1):
    - Flutter
  - screen_brightness_ios (0.1.0):
    - Flutter
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - volume_controller (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - aliyunpan_flutter_sdk_auth (from `.symlinks/plugins/aliyunpan_flutter_sdk_auth/ios`)
  - apple_pay_plugin (from `.symlinks/plugins/apple_pay_plugin/ios`)
  - audio_service (from `.symlinks/plugins/audio_service/darwin`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - ffmpeg_kit_flutter_new (from `.symlinks/plugins/ffmpeg_kit_flutter_new/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_mxlogger (from `.symlinks/plugins/flutter_mxlogger/ios`)
  - flutter_plugin_stkouyu (from `.symlinks/plugins/flutter_plugin_stkouyu/ios`)
  - flutter_sound (from `.symlinks/plugins/flutter_sound/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - media_kit_libs_ios_video (from `.symlinks/plugins/media_kit_libs_ios_video/ios`)
  - media_kit_video (from `.symlinks/plugins/media_kit_video/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - receive_sharing_intent (from `.symlinks/plugins/receive_sharing_intent/ios`)
  - screen_brightness_ios (from `.symlinks/plugins/screen_brightness_ios/ios`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - volume_controller (from `.symlinks/plugins/volume_controller/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git:
    - DKImagePickerController
    - DKPhotoGallery
    - flutter_sound_core
    - MXLogger
    - MXLoggerCore
    - SDWebImage
    - SwiftyGif

EXTERNAL SOURCES:
  aliyunpan_flutter_sdk_auth:
    :path: ".symlinks/plugins/aliyunpan_flutter_sdk_auth/ios"
  apple_pay_plugin:
    :path: ".symlinks/plugins/apple_pay_plugin/ios"
  audio_service:
    :path: ".symlinks/plugins/audio_service/darwin"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  ffmpeg_kit_flutter_new:
    :path: ".symlinks/plugins/ffmpeg_kit_flutter_new/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_mxlogger:
    :path: ".symlinks/plugins/flutter_mxlogger/ios"
  flutter_plugin_stkouyu:
    :path: ".symlinks/plugins/flutter_plugin_stkouyu/ios"
  flutter_sound:
    :path: ".symlinks/plugins/flutter_sound/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  media_kit_libs_ios_video:
    :path: ".symlinks/plugins/media_kit_libs_ios_video/ios"
  media_kit_video:
    :path: ".symlinks/plugins/media_kit_video/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  receive_sharing_intent:
    :path: ".symlinks/plugins/receive_sharing_intent/ios"
  screen_brightness_ios:
    :path: ".symlinks/plugins/screen_brightness_ios/ios"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  volume_controller:
    :path: ".symlinks/plugins/volume_controller/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  aliyunpan_flutter_sdk_auth: 6105a4a0a36d4fdb0dbe02c3e4de8578fd5d777e
  apple_pay_plugin: 595c3850147945f4c4194dc240d98eba09c4b241
  audio_service: aa99a6ba2ae7565996015322b0bb024e1d25c6fd
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  ffmpeg_kit_flutter_new: fd5914d838ca5791b32805e3b0c545a99ab96c15
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_mxlogger: fddc9d8b0b4b066c7b60d47174ed8a7c691494cd
  flutter_plugin_stkouyu: e34ca6b0326a039e9e67d083de37a1c5b8fd7d61
  flutter_sound: b9236a5875299aaa4cef1690afd2f01d52a3f890
  flutter_sound_core: 427465f72d07ab8c3edbe8ffdde709ddacd3763c
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  media_kit_libs_ios_video: 5a18affdb97d1f5d466dc79988b13eff6c5e2854
  media_kit_video: 1746e198cb697d1ffb734b1d05ec429d1fcd1474
  MXLogger: 9376fe6c60006399d4c18f78def3a8091acb0631
  MXLoggerCore: 924c7f8dc5b339713b1810af40d1ca06710ac662
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  receive_sharing_intent: 222384f00ffe7e952bbfabaa9e3967cb87e5fe00
  screen_brightness_ios: 9953fd7da5bd480f1a93990daeec2eb42d4f3b52
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  volume_controller: 3657a1f65bedb98fa41ff7dc5793537919f31b12
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: ce7730ce392f58e84a5beb08e6de1cd7294cc4c0

COCOAPODS: 1.16.2
