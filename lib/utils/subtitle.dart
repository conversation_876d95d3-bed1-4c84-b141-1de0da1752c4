import 'dart:io';
import 'package:lsenglish/model/note_model.dart';
import 'package:lsenglish/utils/extension.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/obs.dart';
import 'package:lsenglish/utils/oss.dart';

import '../model/video_time_interval.dart';
import '../net/net.dart';
import 'subtitle/src/core/models.dart';
import 'subtitle/src/utils/subtitle_controller.dart';
import 'subtitle/src/utils/subtitle_provider.dart';

enum SubtitleMode {
  bilingual,
  native,
  target,
}

List<SubtitleMode> modes = [
  SubtitleMode.native,
  SubtitleMode.target,
  SubtitleMode.bilingual,
];

class SubtitleUtil {
  SubtitleUtil._internal();
  static final SubtitleUtil _instance = SubtitleUtil._internal();
  factory SubtitleUtil() => _instance;

  Future<String> uploadSubtitle(
    String? resourceId,
    int? resourceType, {
    String? filePath,
    String? subtitleUrlCanUseDirect,
    bool autoAddSkipWhenSubtitleTargetNone = false,
    List<VideoTimeInterval> skipList = const [],
  }) async {
    var errorResult = '';
    bool containsOnlySymbols(String s) {
      final pattern = RegExp(r'^[^\w\s]+$');
      return pattern.hasMatch(s);
    }

    if (autoAddSkipWhenSubtitleTargetNone && skipList.isEmpty && !filePath.isNullOrEmpty) {
      skipList = [];
      var subtitleController = SubtitleController(provider: SubtitleProvider.fromFile(File(filePath!)));
      await subtitleController.initial();
      for (var i = 0; i < subtitleController.subtitles.length; i++) {
        if (subtitleController.subtitles[i].targetData == '' || containsOnlySymbols(subtitleController.subtitles[i].targetData)) {
          skipList.add(VideoTimeInterval(
              start: subtitleController.subtitles[i].start.inMilliseconds, end: subtitleController.subtitles[i].end.inMilliseconds));
        }
      }
    }
    var url = subtitleUrlCanUseDirect?.isNotEmpty ?? false
        ? subtitleUrlCanUseDirect
        : filePath?.isNotEmpty ?? false
            ? await OssUtil().uploadSubtitleFile(filePath!)
            : "";
    await Net.getRestClient().updateCutsomSubtitle({
      'subtitleUrl': url,
      'resourceId': resourceId,
      'resourceType': resourceType,
      'skips': skipList,
    }).then((v) {
      ObsUtil().uploadSubtitle.value = v.data;
    }).catchError((e) {
      errorResult = e.toString();
      logger("Subtitle Upload Error $errorResult");
    });
    return errorResult;
  }
}

bool isOverlapping(VideoTimeInterval interval, Subtitle subtitle) {
  return (interval.start! <= subtitle.end.inMilliseconds && interval.end! >= subtitle.start.inMilliseconds);
}

List<int> getIndexsFromIntervalList(List<VideoTimeInterval>? skipList, List<Subtitle>? subtitles) {
  if (skipList == null || subtitles == null) {
    return [];
  }
  List<int> indexes = [];
  for (int i = 0; i < subtitles.length; i++) {
    for (var interval in skipList) {
      if (isOverlapping(interval, subtitles[i])) {
        indexes.add(i);
        break;
      }
    }
  }
  return indexes;
}

List<VideoTimeInterval> generateSkipVideoTimeInterval(List<int> skipIndexes, List<Subtitle> subtitles) {
  List<VideoTimeInterval> skipList = [];

  for (var index in skipIndexes) {
    if (index < subtitles.length) {
      var subtitle = subtitles[index];
      skipList.add(VideoTimeInterval(start: subtitle.start.inMilliseconds, end: subtitle.end.inMilliseconds));
    }
  }

  return skipList;
}

void processIntervals2Map(List<VideoTimeInterval> intervals, Map map, List<Subtitle> subtitles) {
  for (var interval in intervals) {
    int left = 0;
    int right = subtitles.length - 1;

    while (left <= right) {
      int mid = left + (right - left) ~/ 2;
      var subtitle = subtitles[mid];

      if (isOverlapping(interval, subtitle)) {
        map[mid] = interval;
        break;
      } else if (subtitle.end.inMilliseconds < (interval.start ?? 0)) {
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }
  }
}

void processNotesIntervals2Map(List<NoteModel> intervals, Map map, List<Subtitle> subtitles) {
  for (var interval in intervals) {
    int left = 0;
    int right = subtitles.length - 1;

    while (left <= right) {
      int mid = left + (right - left) ~/ 2;
      var subtitle = subtitles[mid];

      if (isOverlapping(VideoTimeInterval(start: interval.videoStartTime, end: interval.videoEndTime), subtitle)) {
        map[mid] = interval;
        break;
      } else if (subtitle.end.inMilliseconds < (interval.videoStartTime ?? 0)) {
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }
  }
}
