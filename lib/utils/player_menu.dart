import 'package:lsenglish/config/config.dart';

class PlayerMenuId {
  static const int editSubtitle = 1;
  static const int note = 2;
  static const int ai = 3;
  static const int collect = 4;
  static const int rate = 5;
  static const int eye = 6;
  static const int translate = 7;
  static const int skip = 8;
  static const int more = 9;
}

class PlayerMenu {
  final int id;
  final String title;

  PlayerMenu({
    required this.id,
    required this.title,
  });
}

class PlayerMenuManager {
  // Private constructor
  PlayerMenuManager._();

  // Single instance
  static final PlayerMenuManager _instance = PlayerMenuManager._();

  // Factory constructor to return the same instance
  factory PlayerMenuManager() => _instance;

  // Default order of menu items
  final List<PlayerMenu> _playerMenuItems = [
    PlayerMenu(id: PlayerMenuId.editSubtitle, title: 'EditSubtitle'),
    PlayerMenu(id: PlayerMenuId.note, title: 'Note'),
    PlayerMenu(id: PlayerMenuId.ai, title: 'AI'),
    PlayerMenu(id: PlayerMenuId.collect, title: 'Collect'),
    PlayerMenu(id: PlayerMenuId.rate, title: 'Rate'),
    PlayerMenu(id: PlayerMenuId.eye, title: 'Eye'),
    PlayerMenu(id: PlayerMenuId.translate, title: 'Translate'),
    PlayerMenu(id: PlayerMenuId.skip, title: 'Skip'),
    PlayerMenu(id: PlayerMenuId.more, title: 'More'),
  ];
  List<PlayerMenu> defaultMenu() {
    _playerMenuItems.sort((a, b) => a.id.compareTo(b.id));
    return _playerMenuItems;
  }

  List<PlayerMenu> menuByConfig() {
    return sortMenuItemsByIds(ids: Config().playerConfig.menuSort);
  }

  List<PlayerMenu> sortMenuItemsByIds({List<int> ids = const []}) {
    if (ids.isEmpty) {
      _playerMenuItems.sort((a, b) => a.id.compareTo(b.id));
      return _playerMenuItems;
    }

    return ids.map((id) {
      return _playerMenuItems.firstWhere((item) => item.id == id);
    }).toList();
  }
}
