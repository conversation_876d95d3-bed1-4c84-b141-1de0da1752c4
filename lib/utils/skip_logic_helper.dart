import 'package:lsenglish/utils/log.dart' as log;
import 'package:lsenglish/utils/toast.dart';
/// 跳过逻辑辅助工具类
/// 统一处理字幕跳过相关的逻辑和用户提示
class SkipLogicHelper {
  static void logger(String msg) {
    log.logger("SkipLogicHelper: $msg");
  }

  /// 查找有效的字幕索引（跳过被标记为跳过的字幕）
  /// [startIndex] 起始索引
  /// [isForward] true表示向前查找，false表示向后查找
  /// [skipList] 跳过列表
  /// [totalCount] 字幕总数
  /// 返回 -1 表示没有找到有效索引
  static int findValidSubtitleIndex(
    int startIndex, {
    required bool isForward,
    required List<int> skipList,
    required int totalCount,
  }) {
    if (totalCount == 0) return -1;
    
    // 边界检查
    if (startIndex < 0 || startIndex >= totalCount) return -1;
    
    // 如果当前索引不在跳过列表中，直接返回
    if (!skipList.contains(startIndex)) {
      return startIndex;
    }
    
    // 查找有效索引
    if (isForward) {
      // 向前查找
      for (int i = startIndex + 1; i < totalCount; i++) {
        if (!skipList.contains(i)) {
          return i;
        }
      }
    } else {
      // 向后查找
      for (int i = startIndex - 1; i >= 0; i--) {
        if (!skipList.contains(i)) {
          return i;
        }
      }
    }
    
    return -1; // 没有找到有效索引
  }

  /// 显示跳过相关的用户提示
  /// [direction] 方向："前面" 或 "后面"
  /// [isAllSkipped] 是否所有句子都被跳过
  static void showSkipToast(String direction, {bool isAllSkipped = false}) {
    String message;
    if (isAllSkipped) {
      message = "所有句子都已被设置了跳过";
    } else {
      message = "${direction}的句子都已被设置了跳过";
    }
    
    logger("Showing toast: $message");
    message.toast;
  }

  /// 处理句子切换的跳过逻辑
  /// 返回有效的目标索引，如果没有找到则返回 -1 并显示提示
  static int handleSubtitleNavigation({
    required int targetIndex,
    required bool isForward,
    required List<int> skipList,
    required int totalCount,
    bool showToast = true,
  }) {
    logger("handleSubtitleNavigation: targetIndex=$targetIndex, isForward=$isForward");
    
    var validIndex = findValidSubtitleIndex(
      targetIndex,
      isForward: isForward,
      skipList: skipList,
      totalCount: totalCount,
    );
    
    if (validIndex == -1 && showToast) {
      var direction = isForward ? "后面" : "前面";
      showSkipToast(direction);
    }
    
    return validIndex;
  }

  /// 处理进度条拖动的跳过逻辑
  /// 进度条拖动的特殊处理：默认向前查找，如果后面都被跳过则向前查找
  static SkipResult handleSeekSkipLogic({
    required int currentIndex,
    required List<int> skipList,
    required int totalCount,
    bool showToast = true,
  }) {
    logger("handleSeekSkipLogic: currentIndex=$currentIndex");
    
    if (totalCount == 0 || !skipList.contains(currentIndex)) {
      // 当前索引不在跳过列表中，无需处理
      return SkipResult(validIndex: currentIndex, needsSkip: false);
    }
    
    // 首先尝试向前查找
    var forwardIndex = findValidSubtitleIndex(
      currentIndex,
      isForward: true,
      skipList: skipList,
      totalCount: totalCount,
    );
    
    if (forwardIndex != -1) {
      // 找到了向前的有效索引
      logger("Found forward valid index: $forwardIndex");
      return SkipResult(validIndex: forwardIndex, needsSkip: true);
    }
    
    // 向前没找到，尝试向后查找
    var backwardIndex = findValidSubtitleIndex(
      currentIndex,
      isForward: false,
      skipList: skipList,
      totalCount: totalCount,
    );
    
    if (backwardIndex != -1) {
      // 找到了向后的有效索引
      logger("Found backward valid index: $backwardIndex");
      if (showToast) {
        showSkipToast("后面");
      }
      return SkipResult(validIndex: backwardIndex, needsSkip: true);
    }
    
    // 前后都没找到有效索引
    logger("No valid index found in either direction");
    if (showToast) {
      showSkipToast("", isAllSkipped: true);
    }
    
    return SkipResult(validIndex: -1, needsSkip: true);
  }

  /// 检查指定索引是否需要跳过
  static bool shouldSkip(int index, List<int> skipList) {
    return skipList.contains(index);
  }

  /// 获取跳过列表的统计信息
  static SkipStatistics getSkipStatistics(List<int> skipList, int totalCount) {
    var skipCount = skipList.length;
    var validCount = totalCount - skipCount;
    var skipRatio = totalCount > 0 ? skipCount / totalCount : 0.0;
    
    return SkipStatistics(
      totalCount: totalCount,
      skipCount: skipCount,
      validCount: validCount,
      skipRatio: skipRatio,
    );
  }
}

/// 跳过逻辑处理结果
class SkipResult {
  final int validIndex; // 有效的索引，-1 表示没有找到
  final bool needsSkip; // 是否需要跳过

  SkipResult({
    required this.validIndex,
    required this.needsSkip,
  });

  bool get isValid => validIndex != -1;
}

/// 跳过统计信息
class SkipStatistics {
  final int totalCount; // 总数
  final int skipCount; // 跳过数量
  final int validCount; // 有效数量
  final double skipRatio; // 跳过比例

  SkipStatistics({
    required this.totalCount,
    required this.skipCount,
    required this.validCount,
    required this.skipRatio,
  });

  @override
  String toString() {
    return 'SkipStatistics(total: $totalCount, skip: $skipCount, valid: $validCount, ratio: ${(skipRatio * 100).toStringAsFixed(1)}%)';
  }
}
