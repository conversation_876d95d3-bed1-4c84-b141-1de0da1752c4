import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/size_extension.dart';

extension ToastExtension on String {
  Future<bool?> get toast => Fluttertoast.showToast(
      msg: this,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
      timeInSecForIosWeb: 1,
      backgroundColor: Colors.black87,
      textColor: Colors.white,
      fontSize: 16.0);

  void get toastWithInfoError => toastInfoError(this);
}

extension SnackbarExtension on String {
  void get snackbar => ScaffoldMessenger.of(Get.context!).showSnackBar(
        SnackBar(content: Text(this), duration: const Duration(seconds: 1)),
      );
}

FToast toastInfoError(String msg, {Duration duration = const Duration(milliseconds: 3000)}) {
  return toastInfo(msg, icon: Icons.error, duration: duration);
}

FToast toastInfoSuccess(String msg, {Duration duration = const Duration(milliseconds: 3000)}) {
  return toastInfo(msg, icon: Icons.check, duration: duration);
}

FToast toastInfo(String msg, {IconData icon = Icons.info, Duration duration = const Duration(milliseconds: 3000)}) {
  var fToast = FToast();
  fToast.init(Get.context!);
  fToast.showToast(
      child: Padding(
        padding: EdgeInsets.all(16.whs),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(15.whs),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.whs),
            color: const Color(0xFF1D192B).withValues(alpha: 0.8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 24.whs,
                color: Colors.white,
              ),
              Gap(10.whs),
              Expanded(child: Text(msg, style: Get.textTheme.titleSmall?.copyWith(color: Colors.white))),
            ],
          ),
        ),
      ),
      gravity: ToastGravity.BOTTOM,
      toastDuration: duration,
      positionedToastBuilder: (context, child, gravity) {
        return Positioned(
          left: 0,
          right: 0,
          bottom: 20.whs,
          child: child,
        );
      });
  return fToast;
}
