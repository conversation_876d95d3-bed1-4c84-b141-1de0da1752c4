import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/ffmpeg.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/xfyun_util.dart';
import 'package:path/path.dart' as path;
import 'package:path/path.dart';

import '../model/xfyun_transfer_result/json1best.dart';
import '../model/xfyun_transfer_result/rt.dart';
import '../model/xfyun_transfer_result/st.dart';
import '../model/xfyun_transfer_result/xfyun_transfer_result.dart';
import 'file.dart';
import 'string.dart';
import 'subtitle.dart';
import 'subtitle/src/core/models.dart';
import 'subtitle/src/utils/subtitle_controller.dart';
import 'type.dart';

class XfyunAnalyze {
  final String audioFilePath;
  final String localResourceId;
  RxString loadingText;
  RxString errorText;
  RxInt currentPhaseCount;
  int phaseCount;
  CancelToken? uploadCancelToken;
  CancelToken? resultCancelToken;
  Completer<void>? completer;
  bool isCanceled;
  String orderId;

  XfyunAnalyze({
    required this.audioFilePath,
    required this.localResourceId,
    this.phaseCount = 5,
    int currentPhaseCount = 0,
    this.uploadCancelToken,
    this.resultCancelToken,
    this.completer,
    this.isCanceled = false,
    this.orderId = "",
    String loadingText = '准备处理中...',
    String errorText = '',
  })  : loadingText = loadingText.obs,
        currentPhaseCount = currentPhaseCount.obs,
        errorText = errorText.obs;
}

class Xfyun {
  Xfyun._internal();
  static final Xfyun _instance = Xfyun._internal();
  factory Xfyun() => _instance;

  final String _appid = '1b89f1dc';
  final String _keySecret = '9354084c18e7df4af097a9458f0a3842';
  final String _host = 'https://raasr.xfyun.cn';

  var xfyunAnalyzes = <XfyunAnalyze?>[].obs;
  final RxList<String> completed = <String>[].obs;
  final List<XfyunAnalyze> _queue = [];
  int _concurrentLimit = 1;
  int _currentRunning = 0;

  void configureConcurrency(int limit) {
    _concurrentLimit = limit;
  }

  bool isCompleted(String? localResourceId) {
    return completed.any((item) => item == localResourceId);
  }

  void addAnalyze(XfyunAnalyze analyze) {
    if (xfyunAnalyzes.any((item) => item?.localResourceId == analyze.localResourceId)) {
      if (kDebugMode) {
        print('Duplicate localResourceId: ${analyze.localResourceId}');
      }
      return;
    }
    _queue.add(analyze);
    xfyunAnalyzes.add(analyze);
    xfyunAnalyzes.refresh();
    _processQueue();
  }

  void cancelAnalyze(String? localResourceId) {
    final analyze = xfyunAnalyzes.firstWhereOrNull((item) => item?.localResourceId == localResourceId);
    if (analyze == null) {
      return;
    }
    analyze.uploadCancelToken?.cancel('Cancelled by user');
    analyze.resultCancelToken?.cancel('Cancelled by user');
    analyze.isCanceled = true;
    analyze.completer?.completeError('Operation was canceled');
    _queue.remove(analyze);
    xfyunAnalyzes.remove(analyze);
    xfyunAnalyzes.refresh();
    if (kDebugMode) {
      print('Canceled analysis for: $localResourceId');
    }

    _queue.removeWhere((item) => item.localResourceId == localResourceId);
    xfyunAnalyzes.removeWhere((item) => item?.localResourceId == localResourceId);
    xfyunAnalyzes.refresh();
    if (kDebugMode) {
      print('Canceled analysis for: $localResourceId');
    }
  }

  Future<void> retryAnalyze(String? localResourceId) async {
    final analyze = xfyunAnalyzes.firstWhereOrNull((item) => item?.localResourceId == localResourceId);
    if (analyze == null) {
      return;
    }
    logger("completed = $completed");
    if (xfyunAnalyzes.contains(analyze)) {
      analyze.errorText.value = "";
      _queue.add(analyze);
      _processQueue();
    } else {
      logger("分析尚未完成或已取消，无法重试");
    }
  }

  Future<void> _processQueue() async {
    if (_queue.isEmpty || _currentRunning >= _concurrentLimit) {
      return;
    }

    _currentRunning++;
    final analyze = _queue.removeAt(0);
    analyze.completer = Completer<void>();
    var analyzeResult = false;
    try {
      analyzeResult = await _startAnalyze(analyze);
      if (analyze.isCanceled) {
        return Future.error('Operation was canceled');
      }
      if (analyzeResult) {
        completed.add(analyze.localResourceId);
        completed.refresh();
        analyze.completer?.complete();
        logger("complete------------");
      }
    } catch (e) {
      analyze.errorText.value = '发生异常$e';
      if (kDebugMode) {
        print("异常:${analyze.localResourceId}");
      }
      analyze.completer?.completeError(e);
    } finally {
      if (analyzeResult) {
        xfyunAnalyzes.remove(analyze);
        xfyunAnalyzes.refresh();
      }
      _currentRunning--;
      _processQueue();
    }
    _processQueue();
  }

  Future<bool> _startAnalyze(XfyunAnalyze analyze) async {
    logger("_startAnalyze = --------------");
    try {
      if (analyze.isCanceled) return false;
      var result = await FFmpegUtils().getMediaDuration(analyze.audioFilePath);
      if (result > 60 * 10) {
        analyze.errorText.value = "音视频时长超过10分钟，已限制处理";
        return false;
      }
      var audioFilePath = "";
      var isAudioFile = await FFmpegUtils().isAudioFile(analyze.audioFilePath);
      if (analyze.isCanceled) return false;
      logger("isAudioFile = $isAudioFile");
      if (!isAudioFile) {
        var isVideoFile = await FFmpegUtils().isVideoFile(analyze.audioFilePath);
        if (analyze.isCanceled) return false;
        logger("isVideoFile = $isVideoFile");
        if (!isVideoFile) {
          analyze.errorText.value = "不支持的文件类型";
          return false;
        }
        audioFilePath = "${(await FileUtils().getVideoThumbDir()).path}/${basenameWithoutExtension(analyze.audioFilePath)}.aac";
        if (analyze.isCanceled) return false;
        analyze.loadingText.value = "正在将视频提取出音频...";
        analyze.currentPhaseCount.value = 1;
        var result = await FFmpegUtils().convertVideoToAudio(analyze.audioFilePath, audioFilePath);
        if (analyze.isCanceled) return false;
        if (!result) {
          analyze.errorText.value = "视频转音频失败";
          return false;
        }
      } else {
        audioFilePath = analyze.audioFilePath;
      }
      analyze.loadingText.value = "正在上传解析后的音频...";
      analyze.currentPhaseCount.value = 2;
      if (analyze.orderId == "") {
        var orderId = await Xfyun().uploadFile2Xfyun(analyze.audioFilePath, uploadCancelToken: analyze.uploadCancelToken);
        analyze.orderId = orderId;
        logger("orderId = $orderId");
        if (analyze.isCanceled) return false;
        if (orderId == "") {
          analyze.errorText.value = "视频上传失败";
          return false;
        }
        await Future.delayed(const Duration(seconds: 3));
      }

      analyze.loadingText.value = "正在获取转写结果...";
      analyze.currentPhaseCount.value = 3;
      var orderResult = await Xfyun().getResult(analyze.orderId, resultCancelToken: analyze.resultCancelToken);
      if (analyze.isCanceled) return false;
      if (orderResult == "") {
        analyze.errorText.value = "获取结果失败失败";
        return false;
      }
      analyze.loadingText.value = "正在解析为字幕...";
      analyze.currentPhaseCount.value = 4;
      _doSubtitle(analyze, orderResult);
      return true;
    } catch (e) {
      if (!analyze.isCanceled) {
        analyze.errorText.value = '处理失败$e';
        analyze.completer?.completeError(e);
      }
      return false;
    }
  }

  Future<void> _doSubtitle(XfyunAnalyze analyze, String orderResult, {bool splitByPeriod = true}) async {
    var xfyunTransferResult = XfyunTransferResult.fromJson(jsonDecode(orderResult));
    SubtitleController subtitleController = SubtitleController();
    List<Subtitle> subtitles = [];
    if (xfyunTransferResult.lattice != null) {
      if (splitByPeriod) {
        final tempSentenceList = <String>[];
        int? startTime;
        int? endTime;
        for (var i = 0; i < xfyunTransferResult.lattice!.length; i++) {
          var lattice = xfyunTransferResult.lattice![i];
          St? st;
          if (lattice.json1best is Map) {
            st = Json1best.fromJson(lattice.json1best).st;
          } else {
            st = Json1best.fromJson(jsonDecode(lattice.json1best)).st;
          }
          List<Rt>? rtList = st?.rt;
          rtList?.forEach((rt) {
            rt.ws?.forEach((ws) {
              ws.cw?.forEach((cw) {
                if (cw.wp == 'p' && cw.w?.trim() == '.') {
                  tempSentenceList.add('.');
                  var tempSentence = tempSentenceList.join(' ');
                  endTime ??= int.tryParse(st?.ed ?? "0");
                  var splitPair = splitText(tempSentence);
                  subtitles.add(Subtitle(
                      subtitleIndex: i,
                      start: Duration(milliseconds: startTime ?? 0),
                      end: Duration(milliseconds: endTime ?? 0),
                      data: tempSentence,
                      targetData: splitPair.first,
                      nativeData: splitPair.second,
                      ids: [i]));
                  tempSentenceList.clear();
                  startTime = null;
                  endTime = null;
                } else {
                  tempSentenceList.add(cw.w?.replaceAll(" ", '') ?? '');
                  startTime ??= int.tryParse(st?.bg ?? "0");
                }
              });
            });
          });
        }
      } else {
        for (var i = 0; i < xfyunTransferResult.lattice!.length; i++) {
          if (analyze.isCanceled) return;
          var lattice = xfyunTransferResult.lattice![i];
          St? st;
          if (lattice.json1best is Map) {
            st = Json1best.fromJson(lattice.json1best).st;
          } else {
            st = Json1best.fromJson(jsonDecode(lattice.json1best)).st;
          }
          List<Rt>? rtList = st?.rt;
          final sentences = <String>[];
          rtList?.forEach((rt) {
            rt.ws?.forEach((ws) {
              final sentence = ws.cw?.map((cw) => cw.w?.replaceAll(" ", '')).join('') ?? '';
              sentences.add(sentence);
            });
          });
          var joinSentence = sentences.join(' ');
          var splitPair = splitText(joinSentence);
          subtitles.add(Subtitle(
              subtitleIndex: i,
              start: Duration(milliseconds: int.tryParse(st?.bg ?? "0") ?? 0),
              end: Duration(milliseconds: int.tryParse(st?.ed ?? "0") ?? 0),
              data: joinSentence,
              targetData: splitPair.first,
              nativeData: splitPair.second,
              ids: [i]));
        }
      }
    }

    subtitleController.setSubtitles(subtitles);
    var subtitlePath = "${(await FileUtils().getSaveSubtitleDir()).path}/${basenameWithoutExtension(analyze.audioFilePath)}.srt";
    if (analyze.isCanceled) return;
    logger("subtitlePath = $subtitlePath");
    subtitleController.writeToSrtFile(subtitlePath);
    analyze.loadingText.value = "字幕解析完毕,正在上传云端...";
    await Future.delayed(const Duration(milliseconds: 500));
    await SubtitleUtil()
        .uploadSubtitle(analyze.localResourceId, ResourceType.localResource.index, filePath: subtitlePath, autoAddSkipWhenSubtitleTargetNone: true);
    analyze.currentPhaseCount.value = 5;
  }

  Future<String> uploadFile2Xfyun(String audioFilePath, {CancelToken? uploadCancelToken}) async {
    final audio = File(audioFilePath);
    final fileName = path.basename(audio.path);
    final fileSize = await audio.length();
    final map = <String, dynamic>{
      'appId': _appid,
      'fileSize': fileSize,
      'fileName': fileName,
      'duration': '200',
      'language': 'en',
      'signa': await LfasrSignature(_appid, _keySecret).getSigna(),
      'ts': LfasrSignature(_appid, _keySecret).getTs(),
    };

    try {
      final dio = Dio();
      var paramString = _parseMapToPathParam(map);
      var url = '$_host/v2/api/upload?$paramString';
      final data = await audio.readAsBytes();
      var orderId = "";
      var response = await dio.post(
        url,
        options: Options(
          headers: {"Content-Type": "application/json"},
        ),
        data: data,
        cancelToken: uploadCancelToken,
      );
      orderId = (response.data as Map<String, dynamic>)['content']['orderId'];
      return orderId;
    } catch (e) {
      return "";
    }
  }

  String _parseMapToPathParam(Map<String, dynamic> map) {
    return map.entries.map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}').join('&');
  }

  Future<String> getResult(String orderId, {CancelToken? resultCancelToken}) async {
    final dio = Dio();
    final resultMap = <String, dynamic>{
      'appId': _appid,
      'resultType': 'transfer',
      'orderId': orderId,
      'signa': await LfasrSignature(_appid, _keySecret).getSigna(),
      'ts': LfasrSignature(_appid, _keySecret).getTs(),
    };
    var paramString = _parseMapToPathParam(resultMap);
    var url = '$_host/v2/api/getResult?$paramString';
    var resultResponse = await dio.post(
      url,
      options: Options(
        headers: {"Content-Type": "application/json"},
      ),
      cancelToken: resultCancelToken,
    );
    try {
      final result = resultResponse.data as Map<String, dynamic>;
      var orderResult = result['content']['orderResult'];
      return orderResult;
    } catch (e) {
      return "";
    }
  }
}
