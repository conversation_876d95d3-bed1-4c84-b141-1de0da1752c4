import 'dart:async';
import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';
import 'package:flutter/material.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/video/media_player.dart';

class MyAudioHandler extends BaseAudioHandler with <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>k<PERSON><PERSON><PERSON>, WidgetsBindingObserver {
  static MyAudioHandler? _instance;
  static MyAudioHandler get instance => _instance!;

  // 订阅管理
  final List<StreamSubscription> _subscriptions = [];

  // 播放器引用
  MediaPlayer? _mediaPlayer;

  // 当前媒体信息
  MediaItem _currentMediaItem = const MediaItem(
    id: 'video_player',
    album: "Video Player",
    title: "No media",
    artist: "LS English",
    duration: Duration.zero,
  );

  // 音频会话
  AudioSession? _audioSession;

  // 私有构造函数
  MyAudioHandler._();

  /// 单例初始化
  static Future<MyAudioHandler> init() async {
    if (_instance != null) {
      logger("MyAudioHandler already initialized");
      return _instance!;
    }

    logger("MyAudioHandler initializing...");
    _instance = MyAudioHandler._();
    await _instance!._initialize();
    return _instance!;
  }

  /// 内部初始化
  Future<void> _initialize() async {
    logger("MyAudioHandler _initialize");

    // 初始化音频会话
    await _initAudioSession();

    // 设置初始媒体项
    mediaItem.add(_currentMediaItem);

    // 设置初始播放状态
    playbackState.add(PlaybackState(
      controls: [
        MediaControl.rewind,
        MediaControl.play,
        MediaControl.fastForward,
      ],
      systemActions: const {
        MediaAction.seek,
        MediaAction.seekForward,
        MediaAction.seekBackward,
      },
      androidCompactActionIndices: const [0, 1, 2],
      processingState: AudioProcessingState.idle,
      playing: false,
      updatePosition: Duration.zero,
      bufferedPosition: Duration.zero,
      speed: 1.0,
    ));

    // 添加生命周期监听
    WidgetsBinding.instance.addObserver(this);

    logger("MyAudioHandler initialized successfully");
  }

  /// 初始化音频会话
  Future<void> _initAudioSession() async {
    try {
      _audioSession = await AudioSession.instance;
      await _audioSession!.configure(const AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playback,
        avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.allowBluetooth,
        avAudioSessionMode: AVAudioSessionMode.defaultMode,
        avAudioSessionRouteSharingPolicy: AVAudioSessionRouteSharingPolicy.defaultPolicy,
        avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
        androidAudioAttributes: AndroidAudioAttributes(
          contentType: AndroidAudioContentType.movie,
          flags: AndroidAudioFlags.none,
          usage: AndroidAudioUsage.media,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
        androidWillPauseWhenDucked: true,
      ));

      // 监听音频中断事件
      _subscriptions.add(_audioSession!.interruptionEventStream.listen(_handleInterruption));
      _subscriptions.add(_audioSession!.becomingNoisyEventStream.listen((_) => _handleBecomingNoisy()));

      logger("Audio session configured successfully");
    } catch (e) {
      logger("Failed to configure audio session: $e");
    }
  }

  /// 处理音频中断
  void _handleInterruption(AudioInterruptionEvent event) {
    logger("Audio interruption: ${event.type}");
    switch (event.type) {
      case AudioInterruptionType.pause:
        pause();
        break;
      case AudioInterruptionType.duck:
        // 只有在之前正在播放时才恢复
        if (_mediaPlayer?.isPlaying() == true) {
          play();
        }
        break;
      default:
        break;
    }
  }

  /// 处理音频变得嘈杂（如拔出耳机）
  void _handleBecomingNoisy() {
    logger("Audio becoming noisy, pausing playback");
    pause();
  }

  /// 更新媒体项信息
  void _updateMediaItem({
    String? title,
    String? artist,
    String? album,
    Duration? duration,
    Uri? artUri,
  }) {
    _currentMediaItem = _currentMediaItem.copyWith(
      title: title ?? _currentMediaItem.title,
      artist: artist ?? _currentMediaItem.artist,
      album: album ?? _currentMediaItem.album,
      duration: duration ?? _currentMediaItem.duration,
      artUri: artUri ?? _currentMediaItem.artUri,
    );
    mediaItem.add(_currentMediaItem);
  }

  /// 更新播放状态
  void _updatePlaybackState({
    bool? playing,
    AudioProcessingState? processingState,
    Duration? position,
    Duration? bufferedPosition,
    double? speed,
  }) {
    final currentState = playbackState.value;
    final newState = currentState.copyWith(
      playing: playing ?? currentState.playing,
      processingState: processingState ?? currentState.processingState,
      updatePosition: position ?? currentState.updatePosition,
      bufferedPosition: bufferedPosition ?? currentState.bufferedPosition,
      speed: speed ?? currentState.speed,
      controls: _getControls(playing ?? currentState.playing),
    );
    playbackState.add(newState);
  }

  /// 获取控制按钮
  List<MediaControl> _getControls(bool isPlaying) {
    return [
      MediaControl.rewind,
      isPlaying ? MediaControl.pause : MediaControl.play,
      MediaControl.fastForward,
    ];
  }

  /// 绑定播放器
  void bindPlayer(MediaPlayer mediaPlayer) {
    logger("MyAudioHandler bindPlayer");

    // 清理之前的订阅
    _clearPlayerSubscriptions();

    // 设置新的播放器
    _mediaPlayer = mediaPlayer;

    // 订阅播放器状态变化
    _subscribeToPlayerEvents();
  }

  /// 清理播放器订阅
  void _clearPlayerSubscriptions() {
    for (var subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
  }

  /// 订阅播放器事件
  void _subscribeToPlayerEvents() {
    if (_mediaPlayer == null) return;

    final player = _mediaPlayer!.getPlayer;

    // 监听播放状态变化
    _subscriptions.add(player.stream.playing.listen((isPlaying) {
      _updatePlaybackState(playing: isPlaying);
    }));

    // 监听时长变化
    _subscriptions.add(player.stream.duration.listen((duration) {
      _updateMediaItem(duration: duration);
    }));

    // 监听位置变化（限制更新频率）
    _subscriptions.add(player.stream.position.listen((position) {
      _updatePlaybackState(position: position);
    }));

    // 监听缓冲位置变化
    _subscriptions.add(player.stream.buffer.listen((buffer) {
      _updatePlaybackState(bufferedPosition: buffer);
    }));

    // 监听播放完成
    _subscriptions.add(player.stream.completed.listen((completed) {
      if (completed) {
        _updatePlaybackState(
          playing: false,
          processingState: AudioProcessingState.completed,
        );
      }
    }));
  }

  /// 设置为就绪状态
  void setReady() {
    logger("MyAudioHandler setReady");
    _updatePlaybackState(
      processingState: AudioProcessingState.ready,
      playing: _mediaPlayer?.isPlaying() ?? false,
    );
  }

  /// 设置为空闲状态
  void setIdle() {
    logger("MyAudioHandler setIdle");
    _clearPlayerSubscriptions();
    _updatePlaybackState(
      processingState: AudioProcessingState.idle,
      playing: false,
    );
    _deactivateAudioSession();
  }

  /// 设置为缓冲状态
  void setBuffering() {
    logger("MyAudioHandler setBuffering");
    _updatePlaybackState(processingState: AudioProcessingState.buffering);
  }

  /// 设置为完成状态
  void setCompleted() {
    logger("MyAudioHandler setCompleted");
    _updatePlaybackState(
      processingState: AudioProcessingState.completed,
      playing: false,
    );
  }

  /// 更新媒体信息
  void updateMediaInfo({
    String? title,
    String? artist,
    String? album,
    Duration? duration,
    String? artworkPath,
  }) {
    logger("MyAudioHandler updateMediaInfo: title=$title, duration=$duration");

    Uri? artUri;
    if (artworkPath != null && artworkPath.isNotEmpty) {
      artUri = Uri.file(artworkPath);
    }

    _updateMediaItem(
      title: title,
      artist: artist,
      album: album,
      duration: duration,
      artUri: artUri,
    );
  }

  /// 激活音频会话
  Future<void> _activateAudioSession() async {
    try {
      if (_audioSession != null) {
        await _audioSession!.setActive(true);
        logger("Audio session activated");
      }
    } catch (e) {
      logger("Failed to activate audio session: $e");
    }
  }

  /// 停用音频会话
  Future<void> _deactivateAudioSession() async {
    try {
      if (_audioSession != null) {
        await _audioSession!.setActive(false);
        logger("Audio session deactivated");
      }
    } catch (e) {
      logger("Failed to deactivate audio session: $e");
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    logger("App lifecycle state changed: $state");

    switch (state) {
      case AppLifecycleState.paused:
        // 应用进入后台，保持音频服务活跃
        break;
      case AppLifecycleState.resumed:
        // 应用恢复前台，确保状态同步
        if (_mediaPlayer != null) {
          _updatePlaybackState(playing: _mediaPlayer!.isPlaying());
        }
        break;
      case AppLifecycleState.detached:
        // 应用即将终止，清理资源
        setIdle();
        break;
      default:
        break;
    }
  }

  /// 获取当前播放器
  MediaPlayer? get currentPlayer => _mediaPlayer;

  @override
  Future<void> play() async {
    logger("MyAudioHandler play");
    if (_mediaPlayer == null) {
      logger("No media player bound");
      return;
    }

    await _activateAudioSession();
    await _mediaPlayer!.play();
    _updatePlaybackState(playing: true);
  }

  @override
  Future<void> pause() async {
    logger("MyAudioHandler pause");
    if (_mediaPlayer == null) {
      logger("No media player bound");
      return;
    }

    await _mediaPlayer!.pause();
    _updatePlaybackState(playing: false);
  }

  @override
  Future<void> stop() async {
    logger("MyAudioHandler stop");
    if (_mediaPlayer == null) {
      logger("No media player bound");
      return;
    }

    _mediaPlayer!.stop();
    _updatePlaybackState(
      playing: false,
      processingState: AudioProcessingState.idle,
      position: Duration.zero,
    );
    await _deactivateAudioSession();
  }

  @override
  Future<void> seek(Duration position) async {
    logger("MyAudioHandler seek to $position");
    if (_mediaPlayer == null) {
      logger("No media player bound");
      return;
    }

    await _mediaPlayer!.seek(position);
    _updatePlaybackState(position: position);
  }

  @override
  Future<void> skipToQueueItem(int index) async {
    logger("MyAudioHandler skipToQueueItem: $index");
    // 视频播放器通常不需要队列功能，但可以扩展
    return super.skipToQueueItem(index);
  }

  /// 兼容旧接口的方法
  void ready() => setReady();
  void idle() => setIdle();
  void buffering() => setBuffering();
  void completed() => setCompleted();

  /// 清理资源
  void dispose() {
    logger("MyAudioHandler dispose");
    _clearPlayerSubscriptions();
    WidgetsBinding.instance.removeObserver(this);
    _deactivateAudioSession();
    _mediaPlayer = null;
  }
}
