import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish/model/video_time_interval.dart';
import 'plan_resource_resp.dart';

part 'plan_day_resp.g.dart';

@JsonSerializable()
class PlanDayResp {
  final String id;
  final int? weekDayNumber;
  final int? planDayNumber;
  final int? studyTimestamp;
  final int? status;
  final int? progress;
  final int? currentSentences;
  final int? targetSentences;
  final int? type;
  final int? subtitleStartTimestamp;
  final int? subtitleEndTimestamp;
  final List<VideoTimeInterval>? recordedRanges;
  final List<PlanResourceResp>? resources;
  final int? averageScore;
  final int? totalLearnDuration;

  PlanDayResp({
    required this.id,
    required this.weekDayNumber,
    this.planDayNumber,
    this.studyTimestamp,
    this.status,
    this.progress,
    this.currentSentences,
    this.targetSentences,
    this.type,
    this.subtitleStartTimestamp,
    this.subtitleEndTimestamp,
    this.recordedRanges,
    required this.resources,
    this.averageScore,
    this.totalLearnDuration,
  });

  factory PlanDayResp.fromJson(Map<String, dynamic> json) => _$PlanDayRespFromJson(json);

  Map<String, dynamic> toJson() => _$PlanDayRespToJson(this);
}
