import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'learning_data_models.g.dart';

/// 学习行为事件类型
enum LearningEventType {
  play('play'),
  record('record');

  const LearningEventType(this.value);
  final String value;
}

/// 句子学习记录
@JsonSerializable()
class SentenceLearningRecord {
  @Json<PERSON><PERSON>(name: 'subtitleStartTime')
  final int subtitleStartTime;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'subtitleEndTime')
  final int subtitleEndTime;

  @<PERSON>son<PERSON>ey(name: 'subtitleDuration')
  final int subtitleDuration;

  @<PERSON>son<PERSON>ey(name: 'playCount')
  int playCount;

  @J<PERSON><PERSON><PERSON>(name: 'playTotalDuration')
  int playTotalDuration; // 毫秒

  @J<PERSON><PERSON><PERSON>(name: 'recordCount')
  int recordCount;

  @<PERSON>sonKey(name: 'recordTotalDuration')
  int recordTotalDuration; // 毫秒

  @<PERSON><PERSON><PERSON><PERSON>(name: 'lastInteractionTime')
  int lastInteractionTime; // 毫秒

  SentenceLearningRecord({
    required this.subtitleStartTime,
    required this.subtitleEndTime,
    required this.subtitleDuration,
    this.playCount = 0,
    this.playTotalDuration = 0,
    this.recordCount = 0,
    this.recordTotalDuration = 0,
    this.lastInteractionTime = 0,
  });

  /// 播放平均时长
  double get playAvgDuration => playCount > 0 ? playTotalDuration / playCount : 0.0;

  /// 录音平均时长
  double get recordAvgDuration => recordCount > 0 ? recordTotalDuration / recordCount : 0.0;

  factory SentenceLearningRecord.fromJson(Map<String, dynamic> json) => _$SentenceLearningRecordFromJson(json);
  Map<String, dynamic> toJson() => _$SentenceLearningRecordToJson(this);
}

/// 学习数据上传模型
@JsonSerializable()
class LearningDataUpload {
  @JsonKey(name: 'resourceId')
  final String resourceId;

  @JsonKey(name: 'resourceType')
  final int resourceType;

  @JsonKey(name: 'sessionStartTime')
  final int sessionStartTime;

  @JsonKey(name: 'sessionEndTime')
  final int sessionEndTime;

  @JsonKey(name: 'totalSessionDuration')
  final int totalSessionDuration; // 会话总时长，毫秒

  @JsonKey(name: 'totalPlayDuration')
  final int totalPlayDuration; // 播放总时长，毫秒

  @JsonKey(name: 'totalRecordDuration')
  final int totalRecordDuration; // 录音总时长，毫秒

  @JsonKey(name: 'lsTimes')
  final int lsTimes;

  @JsonKey(name: 'sentenceRecords')
  final List<SentenceLearningRecord> sentenceRecords;

  LearningDataUpload({
    required this.resourceId,
    required this.resourceType,
    required this.sessionStartTime,
    required this.sessionEndTime,
    required this.totalSessionDuration,
    required this.totalPlayDuration,
    required this.totalRecordDuration,
    required this.lsTimes,
    required this.sentenceRecords,
  });

  factory LearningDataUpload.fromJson(Map<String, dynamic> json) => _$LearningDataUploadFromJson(json);

  Map<String, dynamic> toJson() {
    final json = _$LearningDataUploadToJson(this);
    // 将 sentenceRecords 转换为 JSON 字符串
    json['sentenceRecords'] = jsonEncode(sentenceRecords.map((e) => e.toJson()).toList());
    return json;
  }
}

/// 学习分析结果
@JsonSerializable()
class LearningAnalysisResult {
  @JsonKey(name: 'masteryScore')
  final double masteryScore;

  @JsonKey(name: 'recommendedSentences')
  final List<String> recommendedSentences;

  @JsonKey(name: 'learningInsights')
  final Map<String, dynamic> learningInsights;

  LearningAnalysisResult({
    required this.masteryScore,
    required this.recommendedSentences,
    required this.learningInsights,
  });

  factory LearningAnalysisResult.fromJson(Map<String, dynamic> json) => _$LearningAnalysisResultFromJson(json);
  Map<String, dynamic> toJson() => _$LearningAnalysisResultToJson(this);
}

/// 当前活动状态
class CurrentActivity {
  final LearningEventType eventType;
  final String sentenceId;
  final int startTime;

  CurrentActivity({
    required this.eventType,
    required this.sentenceId,
    required this.startTime,
  });
}
