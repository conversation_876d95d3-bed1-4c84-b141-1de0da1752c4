import 'package:json_annotation/json_annotation.dart';

part 'episode.g.dart';

@JsonSerializable()
class Episode {
  int? id;
  String? uid;
  String? resourceId;
  int? resourceType;
  String? episodeName;
  int? status;
  int? targetLsTimes;
  String? targetDesc;
  int? currentLsTimes;
  int? totalLearnDuration;
  int? totalLearnDayTimes;
  int? currentSentences;
  int? targetSentences;
  double? averageScore;

  Episode({
    this.id,
    this.uid,
    this.resourceId,
    this.resourceType,
    this.episodeName,
    this.status,
    this.targetLsTimes,
    this.targetDesc,
    this.currentLsTimes,
    this.totalLearnDuration,
    this.totalLearnDayTimes,
    this.currentSentences,
    this.targetSentences,
    this.averageScore,
  });

  factory Episode.fromJson(Map<String, dynamic> json) {
    return _$EpisodeFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EpisodeToJson(this);
}
