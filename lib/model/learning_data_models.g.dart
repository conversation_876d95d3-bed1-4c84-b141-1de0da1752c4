// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'learning_data_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SentenceLearningRecord _$SentenceLearningRecordFromJson(
        Map<String, dynamic> json) =>
    SentenceLearningRecord(
      subtitleStartTime: (json['subtitleStartTime'] as num).toInt(),
      subtitleEndTime: (json['subtitleEndTime'] as num).toInt(),
      subtitleDuration: (json['subtitleDuration'] as num).toInt(),
      playCount: (json['playCount'] as num?)?.toInt() ?? 0,
      playTotalDuration: (json['playTotalDuration'] as num?)?.toInt() ?? 0,
      recordCount: (json['recordCount'] as num?)?.toInt() ?? 0,
      recordTotalDuration: (json['recordTotalDuration'] as num?)?.toInt() ?? 0,
      lastInteractionTime: (json['lastInteractionTime'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$SentenceLearningRecordToJson(
        SentenceLearningRecord instance) =>
    <String, dynamic>{
      'subtitleStartTime': instance.subtitleStartTime,
      'subtitleEndTime': instance.subtitleEndTime,
      'subtitleDuration': instance.subtitleDuration,
      'playCount': instance.playCount,
      'playTotalDuration': instance.playTotalDuration,
      'recordCount': instance.recordCount,
      'recordTotalDuration': instance.recordTotalDuration,
      'lastInteractionTime': instance.lastInteractionTime,
    };

LearningDataUpload _$LearningDataUploadFromJson(Map<String, dynamic> json) =>
    LearningDataUpload(
      resourceId: json['resourceId'] as String,
      resourceType: (json['resourceType'] as num).toInt(),
      sessionStartTime: (json['sessionStartTime'] as num).toInt(),
      sessionEndTime: (json['sessionEndTime'] as num).toInt(),
      totalSessionDuration: (json['totalSessionDuration'] as num).toInt(),
      totalPlayDuration: (json['totalPlayDuration'] as num).toInt(),
      totalRecordDuration: (json['totalRecordDuration'] as num).toInt(),
      lsTimes: (json['lsTimes'] as num).toInt(),
      sentenceRecords: (json['sentenceRecords'] as List<dynamic>)
          .map(
              (e) => SentenceLearningRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$LearningDataUploadToJson(LearningDataUpload instance) =>
    <String, dynamic>{
      'resourceId': instance.resourceId,
      'resourceType': instance.resourceType,
      'sessionStartTime': instance.sessionStartTime,
      'sessionEndTime': instance.sessionEndTime,
      'totalSessionDuration': instance.totalSessionDuration,
      'totalPlayDuration': instance.totalPlayDuration,
      'totalRecordDuration': instance.totalRecordDuration,
      'lsTimes': instance.lsTimes,
      'sentenceRecords': instance.sentenceRecords,
    };

LearningAnalysisResult _$LearningAnalysisResultFromJson(
        Map<String, dynamic> json) =>
    LearningAnalysisResult(
      masteryScore: (json['masteryScore'] as num).toDouble(),
      recommendedSentences: (json['recommendedSentences'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      learningInsights: json['learningInsights'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$LearningAnalysisResultToJson(
        LearningAnalysisResult instance) =>
    <String, dynamic>{
      'masteryScore': instance.masteryScore,
      'recommendedSentences': instance.recommendedSentences,
      'learningInsights': instance.learningInsights,
    };
