import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/model/learning_plan_resp/learning_plan_resp.dart';
import 'package:lsenglish/model/learning_plan_resp/plan_day_resp.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/obs.dart';
import 'package:lsenglish/app/modules/plan/widgets/adjust_plan_sheet.dart';

import 'package:lsenglish/utils/toast.dart';

class PlanController extends GetxController {
  final currentPlan = Rx<LearningPlanResp?>(null);
  final isLoading = false.obs;
  final errorMessage = ''.obs;

  // 滚动控制器
  final ScrollController scrollController = ScrollController();
  final AutoScrollController autoScrollController = AutoScrollController();

  // 当前天数信息
  final currentDayIndex = RxInt(-1);
  final currentDay = Rx<PlanDayResp?>(null);

  // 当前天的句子数量（用于动态更新）
  final currentDaySentences = RxInt(0);

  /// 获取今天的匹配计划天数
  PlanDayResp? get todayMatchedDay {
    final plan = currentPlan.value;
    if (plan?.currentDayTimestamp == null || plan?.stages == null) return null;

    for (final stage in plan!.stages!) {
      for (final week in stage.weeks) {
        for (final day in week.days) {
          if (day.studyTimestamp == plan.currentDayTimestamp) {
            return day;
          }
        }
      }
    }
    return null;
  }

  /// 检查今天是否有匹配的学习计划
  bool get hasTodayPlan {
    return todayMatchedDay != null;
  }

  @override
  void onInit() {
    super.onInit();
    // 页面初始化时获取当前计划
    getCurrentPlan();

    // 监听学习进度更新通知，精确更新当前天数据
    ever(ObsUtil().updatePlanProgress, (Map<String, dynamic>? updateData) {
      if (updateData != null && updateData['newSentences'] != null) {
        final totalSentences = updateData['newSentences'] as int;
        logger('收到学习进度更新通知，总句子数: $totalSentences');
        _updateCurrentDaySentences(totalSentences);
      }
    });
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }

  void startLearning(PlanDayResp day) {
    Get.toNamed(Routes.DETAIL, arguments: {
      'videoUrlOrPath': day.resources?.first.resourceUrl,
      'videoName': day.resources?.first.resourceName,
      'resourceId': day.resources?.first.resourceId,
      'dayId': day.id, // 添加dayId参数
    });
  }

  /// 显示选择计划天数弹窗
  void showSelectPlanDaysSheet({bool forceRegenerate = false}) {
    AdjustPlanSheet.showSelectPlanDaysSheet(
      Get.context!,
      onPlanUpdated: (LearningPlanResp? updatedPlan) {
        if (updatedPlan != null) {
          currentPlan.value = updatedPlan;
          logger('计划已更新');
        }
      },
    );
  }

  /// 生成固定学习计划
  Future<void> generateFixedPlan({bool forceRegenerate = false, int totalLearnDays = 14}) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // 调用生成固定计划接口
      final studyDaysOfWeek = [1, 2, 3, 4, 5, 6, 7]..sort(); // 确保学习日按数字排序
      await Net.getRestClient().generateFixedPlan({
        'currentLevel': 'A0', // 当前级别，可以从用户配置或选择中获取
        'targetLevel': 'B1', // 目标级别，可以从用户配置或选择中获取
        'dailyStudyMinutes': 20, // 每日学习时间（分钟）
        'studyDaysOfWeek': studyDaysOfWeek, // 每周学习的具体日期（1-7表示周一到周日）
        'dailySentences': 35, // 每天学习句数目标
        'forceRegenerate': forceRegenerate, // 是否强制重新生成计划
        'totalLearnDays': totalLearnDays, // 总学习天数
      });

      // 生成成功后获取当前计划
      await getCurrentPlan();
      toastInfoSuccess("学习计划生成成功！");
    } catch (e) {
      errorMessage.value = '生成计划失败: ${e.toString()}';
      toastInfoError(errorMessage.value);
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取当前学习计划
  Future<void> getCurrentPlan() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final response = await Net.getRestClient().currentPlan();
      if (response.data != null) {
        // 清理旧的keys，避免重复key问题
        // dayKeys.clear(); // Removed as per edit hint

        // 检查数据中是否有重复的dayId
        _checkForDuplicateDayIds(response.data!);

        currentPlan.value = response.data;
        // 查找当前天数并滚动到对应位置
        _findCurrentDayAndScroll();
      }
    } catch (e) {
      errorMessage.value = '获取计划失败: ${e.toString()}';
      logger('获取当前计划失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 查找当前天数并滚动到对应位置
  void _findCurrentDayAndScroll() {
    final plan = currentPlan.value;
    if (plan?.currentDayTimestamp == null || plan?.stages == null) return;

    int globalDayIndex = 0;

    // 遍历所有阶段、周、天，查找匹配的currentDayTimestamp
    for (final stage in plan!.stages!) {
      for (final week in stage.weeks) {
        for (final day in week.days) {
          if (day.studyTimestamp == plan.currentDayTimestamp) {
            currentDay.value = day;
            currentDayIndex.value = globalDayIndex;

            // 初始化当前天的句子数量
            currentDaySentences.value = day.currentSentences ?? 0;

            _scrollToCurrentDayPrecise(day.id);
            return;
          }
          globalDayIndex++;
        }
      }
    }
  }

  /// 方案1: 使用ValueKey进行精确滚动
  void _scrollToCurrentDayPrecise(String dayId) {
    logger('开始滚动到当前天，dayId: $dayId');
    // 延迟执行，确保UI已经构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToCurrentDayByIndex();
    });
  }

  /// 基于索引滚动到当前天
  void _scrollToCurrentDayByIndex() {
    final plan = currentPlan.value;
    if (plan?.currentDayTimestamp == null || plan?.stages == null) return;

    int targetIndex = 0;
    bool found = false;
    String? currentDayId;

    // 计算当前天在列表中的位置
    for (final stage in plan!.stages!) {
      for (final week in stage.weeks) {
        for (final day in week.days) {
          if (day.studyTimestamp == plan.currentDayTimestamp) {
            found = true;
            currentDayId = day.id;
            break;
          }
          targetIndex++;
        }
        if (found) break;
      }
      if (found) break;
    }

    logger('找到当前天，targetIndex: $targetIndex, currentDayId: $currentDayId');

    // 如果当前天是第一个（targetIndex == 0），则不滚动
    if (targetIndex == 0) {
      logger('当前天是第一个，无需滚动');
      return;
    }

    if (found && autoScrollController.hasClients && currentDayId != null) {
      logger('开始使用 scroll_to_index 滚动');
      // 使用 scroll_to_index 库滚动到指定索引
      autoScrollController.scrollToIndex(
        targetIndex,
        preferPosition: AutoScrollPosition.middle,
        duration: const Duration(milliseconds: 800),
      );
      logger('滚动到索引: $targetIndex');
    } else {
      logger('滚动条件不满足 - found: $found, hasClients: ${autoScrollController.hasClients}, currentDayId: $currentDayId');
    }
  }



  /// 检查数据中是否有重复的dayId
  void _checkForDuplicateDayIds(LearningPlanResp plan) {
    final dayIds = <String>{};
    final duplicateDayIds = <String>{};

    for (final stage in plan.stages ?? []) {
      for (final week in stage.weeks) {
        for (final day in week.days) {
          if (dayIds.contains(day.id)) {
            duplicateDayIds.add(day.id);
            logger('Duplicate dayId found: ${day.id}');
          } else {
            dayIds.add(day.id);
          }
        }
      }
    }

    if (duplicateDayIds.isNotEmpty) {
      logger('Warning: Found duplicate dayIds: $duplicateDayIds');
    }
  }

  /// 显示调整计划弹窗
  void showAdjustPlanSheet() {
    AdjustPlanSheet.show(
      Get.context!,
      plan: currentPlan.value!,
      onPlanUpdated: (LearningPlanResp? updatedPlan) {
        if (updatedPlan != null) {
          currentPlan.value = updatedPlan;
          logger('计划已更新');
        }
      },
    );
  }

  /// 精确更新当前天的句子数量
  void _updateCurrentDaySentences(int totalSentences) {
    final plan = currentPlan.value;
    if (plan?.currentDayTimestamp == null || plan?.stages == null) {
      logger('无法更新句子数量：当前天时间戳为空或计划数据为空');
      return;
    }

    // 如果新值与当前值相等，则不进行赋值操作，避免触发不必要的UI刷新
    if (currentDaySentences.value == totalSentences) {
      logger('句子数量未变化，跳过更新: $totalSentences');
      return;
    }
    if (todayMatchedDay != null && todayMatchedDay!.targetSentences == totalSentences) {
      getCurrentPlan();
      return;
    }
    currentDaySentences.value = totalSentences;
    logger('已更新当前天句子数量: ${currentDaySentences.value} (总句子数: $totalSentences)');
  }
}
