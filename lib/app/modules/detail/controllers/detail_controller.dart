import 'dart:async';
import 'dart:convert';
import 'package:back_button_interceptor/back_button_interceptor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/base/base_controller.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/obs.dart';
import 'package:lsenglish/net/net.dart';

import 'package:lsenglish/utils/login.dart';
import 'package:lsenglish/utils/sp.dart';
import 'package:lsenglish/utils/xfyun.dart';
import 'package:path/path.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import '../../../../widgets/lstimes_mod_widget.dart';
import '../interfaces/detail_state.dart';
import '../modules/player_module.dart';
import '../views/more.dart';
import 'detail_util.dart';
import '../modules/module_manager.dart';
import '../modules/note_module.dart';
import '../modules/subtitle_module.dart';
import '../modules/speech_evaluation_module.dart';
import '../modules/collection_module.dart';
import '../modules/playback_module.dart';
import '../modules/data_module.dart';

class DetailController extends BaseController with GetTickerProviderStateMixin, WidgetsBindingObserver {
  // 模块管理器
  final ModuleManager _moduleManager = ModuleManager();

  // 笔记模块
  final NoteModule _noteModule = NoteModule();

  // 字幕模块
  final SubtitleModule _subtitleModule = SubtitleModule();

  // 语音评测模块
  final SpeechEvaluationModule _speechEvaluationModule = SpeechEvaluationModule();

  // 收藏模块
  final CollectionModule _collectionModule = CollectionModule();

  // 播放控制模块
  final PlaybackModule _playbackModule = PlaybackModule();

  // 数据模块
  final DataModule _dataModule = DataModule();

  // 播放模块
  final PlayerModule _playerModule = PlayerModule();

  final DetailState detailState = DetailState();

  @override
  void onInit() async {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    BackButtonInterceptor.add(_interceptorPop);
    // 初始化 detailState
    detailState.initialize();
    // 初始化模块管理器
    await _initializeModules();
    _playbackModule.initializePlayerMenu();
    detailState.itemScrollController.addListener(_scrollListener);

    _playerModule.checkVideo(detailState.videoUrlOrPath);
    _initListener();
    fetchDetailData();
  }

  @override
  void onReady() {
    super.onReady();
    detailState.videoKit.exitNativeFullscreen();
  }

  void _initListener() async {
    subscriptions.clear();
    subscriptions.addAll([
      detailState.videoKit.currentSubtitleIndex.listen((index) async {
        logger("currentSubtitleIndex listen currentSubtitleIndex= $index _pageChangeByUser=${detailState.pageChangeByUser.value}");
        detailState.currentPage.value = index;
        if (detailState.videoKit.openLsMode.value) {
          if (!detailState.pageChangeByUser.value) {
            //如果是用户自己滑动 不需要做任何处理
            // 当更改了进度之后 也需要stopPlayer
            //1. 当用户主动滑动的时候，如果出现了跳过的逻辑，是需要触发这里的jump的
            _speechEvaluationModule.recordSoundPlayer.stopPlayer();
            _speechEvaluationModule.recognitionService.stop();
            jumpToPage(index);
            _speechEvaluationModule.recordingInLsMode.value = false;
          }
        } else {
          subtitleListScrollToCenter();
        }
      }),
      detailState.videoKit.seekSkipSubtitleIndex.listen((index) {
        logger("seekSkipSubtitleIndex listen index = $index");
        jumpToPage(index);
      }),
      detailState.videoKit.seekByHistoryIndex.listen((index) {
        //这里为了等待pageview先跳转到指定的位置 保证不会跳到第一句然后又跳到对应的进度所对应的index
        logger("seekByHistoryIndex listen index = $index");
        jumpToPage(index);
        detailState.loadingSubtitle.value = false;
        detailState.videoKit.play();
      }),
      ObsUtil().loginStatus.listen((isLogin) {
        if (isLogin) {
          fetchDetailData();
        }
      }),
      Xfyun().completed.listen((data) {
        logger("Xfyun completed = $data");
        if (Xfyun().isCompleted(detailState.localDetailResp?.resourceId)) {
          detailState.loadingSubtitle.value = true;
          fetchDetailData();
        }
      }),
    ]);
  }

  Future<void> checkVideo(String videoPath) async {
    await _playerModule.checkVideo(videoPath);
  }

  void closeLsMode() {
    detailState.videoKit.openLsMode.value = false;
    subtitleListScrollToCenter();
  }

  void openLsMode() {
    detailState.videoKit.openLsMode.value = true;
    jumpToPage(detailState.videoKit.currentSubtitleIndex.value);
  }

  void jumpToPage(int index) {
    if (detailState.isLandscape.value) {
      return;
    }
    if (index >= 0 && index < detailState.videoKit.subtitles.length) {
      if (!detailState.pageController.hasClients) {
        logger("jumpToPage pageController.hasClients = false");
        WidgetsBinding.instance.addPostFrameCallback((_) {
          logger("jumpToPage pageController.jumpToPage index = $index");
          detailState.pageController.jumpToPage(index);
        });
      } else {
        logger("jumpToPage pageController.hasClients = true index = $index");
        detailState.pageController.jumpToPage(index);
      }
    }
  }

  //播放录音的时候需要暂停视频
  void playRecord() async {
    await _playerModule.playRecord();
  }

  void onUserScrollStart() {
    detailState.pageChangeByUser.value = true;
  }

  void onPageScrollDirection(bool isForward) {
    detailState.videoKit.skipFindReverse = !isForward;
  }

  void onUserScrollEnd() {
    detailState.pageChangeByUser.value = false;
  }

  void onUserScroll(UserScrollNotification userScroll) {
    detailState.videoKit.skipFindReverse = userScroll.direction == ScrollDirection.forward;
  }

  //改变index的时候 需要暂停视频、关闭录音 关闭录音播放
  //当更改了进度之后 也需要stopPlayer
  void onPageChanged(int index) async {
    detailState.currentPage.value = index;
    logger("onPageChanged index = $index  _pageChangeByUser= ${detailState.pageChangeByUser.value}");

    if (!detailState.pageChangeByUser.value) {
      return;
    }
    if (_speechEvaluationModule.recordingInLsMode.value) {
      _speechEvaluationModule.recognitionService.stop();
    }
    detailState.videoKit.unmute();
    _speechEvaluationModule.recordSoundPlayer.stopPlayer();

    logger("onPageChanged pause");
    detailState.videoKit.pause(); //为了解决显示loading的问题 先pause 再play
    _speechEvaluationModule.recordingInLsMode.value = false;

    // 当页面切换时，如果标记为需要恢复，则恢复showSubtitlePlaceholder为true
    if (_subtitleModule.shouldRestoreOnSubtitleChange && _subtitleModule.showSubtitlePlaceholder.value == false) {
      _subtitleModule.showSubtitlePlaceholder.value = true;
      _subtitleModule.shouldRestoreOnSubtitleChange = false; // 重置标记
      logger("onPageChanged: 页面切换，恢复showSubtitlePlaceholder为true");
    }

    onSubTitleListClick(index);
  }

  void onSubTitleListClick(int index) {
    // recordText.value = "";
    _playerModule.onSubTitleListClick(index);
  }

  void goMoreSetting() {
    detailState.videoKit.pause();
    _speechEvaluationModule.recognitionService.stop();
    _speechEvaluationModule.recordSoundPlayer.stopPlayer();
    _speechEvaluationModule.recordingInLsMode.value = false;
    Get.bottomSheet(
      const PlayerMoreWidget(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      elevation: 2,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      enableDrag: true,
    );
  }

  void goAIGenSubtitle() async {
    Get.toNamed(Routes.AUDIO_CONVERT, arguments: {
      'videoPath': detailState.videoUrlOrPath,
      'localResourceId': detailState.localDetailResp?.resourceId,
    });
  }

  void subtitleListScrollToCenter() {
    if (detailState.isLandscape.value) {
      return;
    }
    if (!detailState.allowAutoScroll) {
      return;
    }
    detailState.itemScrollController.scrollToIndex(detailState.currentPage.value, preferPosition: AutoScrollPosition.middle);
  }

  void _scrollListener() {
    if (detailState.itemScrollController.position.userScrollDirection == ScrollDirection.reverse) {
      // 向上滑动，隐藏 AppBar
      detailState.isAppBarVisible.value = false;
    } else if (detailState.itemScrollController.position.userScrollDirection == ScrollDirection.forward) {
      detailState.isAppBarVisible.value = true;
    }
  }

  void onPointerDown() {
    detailState.autoScrollTimer?.cancel();
    detailState.allowAutoScroll = false;
  }

  void onPointerUp() {
    detailState.autoScrollTimer = Timer(const Duration(seconds: 3), () {
      detailState.allowAutoScroll = true;
    });
  }

  @override
  void didChangeMetrics() {
    _moduleManager.didChangeMetrics();
    // ignore: deprecated_member_use
    final metrics = WidgetsBinding.instance.window.physicalSize;
    final width = metrics.width;
    final height = metrics.height;
    detailState.isLandscape.value = width > height;
    if (!detailState.isLandscape.value) {
      jumpToPage(detailState.videoKit.currentSubtitleIndex.value);
    } else {
      _playbackModule.lsContainerClick(detailState.videoKit.currentSubtitleIndex.value);
    }
  }

  void onVisibilityLost() {
    _moduleManager.onVisibilityLost();
  }

  void onVisibilityGained() {
    _moduleManager.onVisibilityGained();
  }

  // 笔记
  void goNoteList() async {
    Get.toNamed(Routes.NOTELIST, arguments: {
      'subtitlePath': detailState.videoKit.subtitlePath,
    });
  }

  Future<void> fetchDetailData() async {
    if (!isLogin()) {
      return _subtitleModule.loadSubtitle();
    }
    await Net.getRestClient().getVideoDetail({
      'localVideoPath': detailState.isLocalVideo ? detailState.videoUrlOrPath : "",
      'fileName': detailState.isLocalVideo ? basenameWithoutExtension(filterVideoUrl(detailState.videoUrlOrPath)) : "",
      'resourceId': detailState.remoteResourceId,
      'resourceType': detailState.isLocalVideo ? 2 : 1,
    }).then((v) async {
      logger("getVideoDetail success resp=${jsonEncode(v.data)}");
      detailState.localDetailResp = v.data;
      detailState.videoKit.positionInit = detailState.localDetailResp!.position ?? 0;

      await _moduleManager.onFetchDetailData();
    })
    // .catchError((err) async {
    //   logger("getVideoDetail error err=$err");
    //   await _moduleManager.onFetchDetailData(error: err);
    // })
    ;
  }

  void switchCollect() {
    _collectionModule.switchCollect(detailState.currentPage.value);
  }

  void updateVideoPosition(int position) async {
    if (detailState.localDetailResp == null) {
      loggerError("updateVideoPosition localDetailResp is null");
      return;
    }
    logger("updateVideoPosition position=$position");
    Net.getRestClient().updateVideoPosition({
      'resourceId': detailState.localDetailResp!.resourceId,
      'resourceType': detailState.localDetailResp!.resourceType,
      'position': position,
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    _dataModule.onAppLifecycleStateChanged(state);
  }

  void showLsTimeDialog() {
    showLsTimeModeDialog(detailState.localDetailResp!.resourceId, 2);
  }

  void changeControlMenuSort(int newIndex, int oldIndex) {
    if (newIndex > oldIndex) newIndex -= 1;
    final item = _playbackModule.playerMenuItems.removeAt(oldIndex);
    _playbackModule.playerMenuItems.insert(newIndex, item);
    Config().playerConfig.menuSort = _playbackModule.playerMenuItems.map((item) => item.id).toList();
    Net.getRestClient().updatePlayerConfig({'menuSort': Config().playerConfig.menuSort});
  }

  void onSliderChange(double ratio) async {
    await _playerModule.onSliderChange(ratio);
  }

  // 返回 false 表示不继续传递事件
  bool _interceptorPop(bool stopDefaultButtonEvent, RouteInfo info) {
    if (Get.context?.isLandscape == true) {
      detailState.videoKit.toggleFullscreen();
      return true;
    }
    return false;
  }

  /// 初始化模块管理器
  Future<void> _initializeModules() async {
    logger("DetailController: 开始初始化模块管理器");
    // 注册播放模块
    _playerModule.setController(this);
    _moduleManager.registerModule('player', _playerModule);
    // 注册笔记模块
    _noteModule.setController(this);
    _moduleManager.registerModule('note', _noteModule);

    // 注册字幕模块
    _subtitleModule.setController(this);
    _moduleManager.registerModule('subtitle', _subtitleModule);

    // 注册语音评测模块
    _speechEvaluationModule.setController(this);
    _moduleManager.registerModule('speech_evaluation', _speechEvaluationModule);

    // 注册收藏模块
    _collectionModule.setController(this);
    _moduleManager.registerModule('collection', _collectionModule);

    // 注册播放控制模块
    _playbackModule.setController(this);
    _moduleManager.registerModule('playback', _playbackModule);

    // 注册数据模块
    _dataModule.setController(this);
    _moduleManager.registerModule('data', _dataModule);

    // 初始化所有模块
    await _moduleManager.initialize();

    logger("DetailController: 模块管理器初始化完成");
  }

  /// 获取笔记模块
  NoteModule get noteModule => _noteModule;

  /// 获取字幕模块
  SubtitleModule get subtitleModule => _subtitleModule;

  /// 获取语音评测模块
  SpeechEvaluationModule get speechEvaluationModule => _speechEvaluationModule;

  /// 获取收藏模块
  CollectionModule get collectionModule => _collectionModule;

  /// 获取播放控制模块
  PlaybackModule get playbackModule => _playbackModule;

  /// 获取数据模块
  DataModule get dataModule => _dataModule;

  /// 获取播放模块
  PlayerModule get playerModule => _playerModule;

  @override
  void onClose() async {
    BackButtonInterceptor.remove(_interceptorPop);
    updateVideoPosition(detailState.videoKit.currentPositionInMilliseconds);
    SPUtil().saveSubtitleCover(detailState.videoUrlOrPath, Config().subtitleCoverModel);
    ObsUtil().watchHistoryPositionChange.value = [detailState.localDetailResp?.resourceId ?? "", detailState.videoKit.currentPositionInMilliseconds];
    WidgetsBinding.instance.removeObserver(this);

    // 先销毁模块管理器（包含播放器模块）
    await _moduleManager.dispose();

    super.onClose();
  }
}
