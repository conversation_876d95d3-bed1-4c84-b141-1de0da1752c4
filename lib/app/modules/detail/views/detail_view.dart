import 'package:flutter/material.dart';
import 'package:focus_detector/focus_detector.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/size_extension.dart';

// 导入使用基础组件的组件
import 'widgets/base_detail_widget.dart';
import 'widgets/video_player_widget.dart';
import 'widgets/subtitle_content_widget.dart';
import 'widgets/voice_control_widget.dart';
import 'widgets/player_control_widget.dart';
import 'widgets/landscape_view_widget.dart';
import 'widgets/progress_widget.dart';
import '../controllers/detail_controller.dart';

/// Detail页面主视图 - 使用BaseDetailWidget版本
/// 职责：协调各个子组件，处理横竖屏切换逻辑
class DetailView extends BaseDetailWidget {
  const DetailView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FocusDetector(
      onVisibilityLost: () {
        if (Get.isRegistered<DetailController>()) {
          controller.onVisibilityLost();
        }
      },
      onVisibilityGained: () {
        controller.onVisibilityGained();
      },
      child: OrientationBuilder(
        builder: (BuildContext context, Orientation orientation) {
          return Obx(() {
            // 横屏模式 - 使用基类访问器
            if (isLandscape) {
              return const LandscapeViewWidget();
            }

            // 竖屏模式
            return _buildPortraitView();
          });
        },
      ),
    );
  }

  /// 竖屏视图
  Widget _buildPortraitView() {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          _buildAppBar(),
          _buildVideoSection(),
          Gap(6.whs),
          // 字幕内容区域 - 使用新版本组件
          const Expanded(child: SubtitleContentWidget()),
          // 语音控制区域 - 使用基类访问器和新版本组件
          Obx(() => Visibility(
                visible: !isLoading, // 使用基类访问器
                child: const VoiceControlWidget(),
              )),
          Gap(8.whs),
          // 播放器控制区域 - 使用新版本组件
          const PlayerControlWidget(),
        ],
      ),
    );
  }

  /// 应用栏
  Widget _buildAppBar() {
    return Obx(() => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: detailState.isAppBarVisible.value ? 100 : 56, // 使用基类访问器
          child: AppBar(
            backgroundColor: Colors.white,
            actions: [
              // GestureDetector(
              //     onTap: () => Get.to(() => const LsModeSettingWidget()),
              //     child: const Text("LS", style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18))),
              Padding(
                padding: const EdgeInsets.only(right: 16),
                child: _buildMoreWidget(),
              ),
            ],
          ),
        ));
  }

  /// 视频区域
  Widget _buildVideoSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: const Color(0xffEFEFF0),
        ),
        child: const VideoPlayerWidget(),
      ),
    );
  }

  /// LS模式切换更多菜单
  Widget _buildMoreWidget() {
    return const ProgressWidget();
    // return Obx(() {
    //   // 使用基类访问器
    //   var titles = ["新增LS完成遍数", isLsMode ? "切换到仅听模式" : "切换到LS模式"];
    //   var icons = [R.plus, R.alignleft];
    //   return TitleMoreWidget(
    //     titles: titles,
    //     icons: icons,
    //     titleMoreCallback: (index) {
    //       if (index == 0) {
    //         controller.showLsTimeDialog();
    //       } else if (index == 1) {
    //         if (isLsMode) { // 使用基类访问器
    //           controller.closeLsMode();
    //         } else {
    //           controller.openLsMode();
    //         }
    //       }
    //     },
    //     color: color,
    //   );
    // });
  }
}
