import 'package:flutter/material.dart';
import 'package:flutter_chat_core/flutter_chat_core.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class MarkdownMessageWidget extends StatelessWidget {
  final Message message;
  final int index;
  final bool isSentByMe;
  final MessageGroupStatus? groupStatus;

  const MarkdownMessageWidget({
    super.key,
    required this.message,
    required this.index,
    required this.isSentByMe,
    this.groupStatus,
  });

  @override
  Widget build(BuildContext context) {
    final textMessage = message as TextMessage;

    return Container(
      margin: EdgeInsets.only(
        left: isSentByMe ? 50 : 16,
        right: isSentByMe ? 16 : 50,
        top: 8,
        bottom: 8,
      ),
      child: Row(
        mainAxisAlignment: isSentByMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 消息内容
          Flexible(
            child: isSentByMe
                ? Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      textMessage.text,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: _buildMarkdownContent(textMessage.text)),
                      if (!isSentByMe) ...[
                        const SizedBox(height: 12),
                        _buildActionButtons(),
                      ],
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildMarkdownContent(String text) {
    return MarkdownBody(
      data: text,
      styleSheet: MarkdownStyleSheet(
        p: TextStyle(
          color: Colors.grey.shade800,
          fontSize: 16,
          height: 1.4,
        ),
        strong: TextStyle(
          color: Colors.grey.shade900,
          fontWeight: FontWeight.bold,
        ),
        em: TextStyle(
          color: Colors.grey.shade800,
          fontStyle: FontStyle.italic,
        ),
        code: TextStyle(
          color: Colors.blue.shade700,
          backgroundColor: Colors.blue.shade50,
          fontFamily: 'monospace',
        ),
        codeblockDecoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        blockquote: TextStyle(
          color: Colors.grey.shade600,
          fontStyle: FontStyle.italic,
        ),
        blockquoteDecoration: BoxDecoration(
          border: Border(
            left: BorderSide(
              color: Colors.blue.shade300,
              width: 4,
            ),
          ),
        ),
      ),
      shrinkWrap: true,
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        _buildActionButton(
          icon: Icons.edit,
          onTap: () {
            // 编辑功能
          },
        ),
        const SizedBox(width: 16),
        _buildActionButton(
          icon: Icons.copy,
          onTap: () {
            // 复制功能
          },
        ),
        const SizedBox(width: 16),
        _buildActionButton(
          icon: Icons.thumb_up,
          onTap: () {
            // 点赞功能
          },
        ),
        const SizedBox(width: 16),
        _buildActionButton(
          icon: Icons.thumb_down,
          onTap: () {
            // 点踩功能
          },
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Icon(
        icon,
        size: 20,
        color: Colors.grey.shade600,
      ),
    );
  }
}
