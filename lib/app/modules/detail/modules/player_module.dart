import 'dart:async';
import 'dart:io';
import 'package:audio_service/audio_service.dart';
import 'package:back_button_interceptor/back_button_interceptor.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart' as fcm;
import 'package:get/get.dart';
import 'package:lsenglish/app/modules/detail/controllers/detail_util.dart';
import 'package:lsenglish/app/modules/detail/modules/base_module.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/oss.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:lsenglish/utils/video.dart';
import 'package:lsenglish/video/audio_handler.dart';
import 'package:lsenglish/video/player.dart';
import 'package:lsenglish/widgets/base_dialog.dart';

class PlayerModule extends DefaultModule {
  PlayerModule() : super('PlayerModule');

  @override
  Future<void> onInitialize() async {
    logger("$moduleName: 初始化播放模块");
    _initPlayerListeners();
  }

  @override
  Future<void> onDispose() async {
    logger("$moduleName: 销毁播放模块");
    await closePlayer();
  }

  @override
  Future<void> onFetchDetailData(Exception? error) async {  
    logger("$moduleName: onFetchDetailData called");
    IPlayer.audioHandler = await AudioService.init(
      builder: () => MyAudioHandler(videoKit),
      config: const AudioServiceConfig(
        androidNotificationChannelId: 'com.mikaelzero.lsenglish',
        androidNotificationChannelName: 'Music playback',
      ),
    );
    if (detailState.isLocalVideo) {
      // 检查本地文件是否存在
      final videoFile = File(detailState.videoUrlOrPath);
      bool localFileExists = await videoFile.exists();

      if (!localFileExists && detailState.localDetailResp?.playUrl != null && detailState.localDetailResp!.playUrl!.isNotEmpty) {
        logger("本地文件不存在，使用服务器远程地址: ${detailState.localDetailResp!.playUrl}");
        detailState.videoUrlOrPath = detailState.localDetailResp!.playUrl!;
        await videoKit.open(detailState.videoUrlOrPath);
        downloadVideoCache(detailState.videoUrlOrPath);
      } else if (!localFileExists) {
        logger("本地文件不存在且服务器无远程地址");
        "视频文件不存在".toast;
        return;
      } else if (detailState.localDetailResp?.playUrl == null || detailState.localDetailResp!.playUrl!.isEmpty) {
        // 本地文件存在但服务器无远程地址，提示上传
        videoKit.pause();
        await Get.dialog(
          CommonDialog(title: "检测到本地视频资源，是否上传到服务端以便同步学习进度？", options: const [
            "上传",
            "暂不上传"
          ], callbacks: [
            () async {
              logger("用户选择上传本地视频到服务端");
              "正在后台上传视频文件，成功后会通知您".toast;
              _uploadVideoInBackground(detailState.videoUrlOrPath);
            },
            () {
              logger("用户选择暂不上传本地视频");
            }
          ]),
          barrierDismissible: false,
        );
      }
    } else {
      detailState.videoUrlOrPath = detailState.localDetailResp!.playUrl ?? "";
      var cacheVideoFile = await fcm.DefaultCacheManager().getFileFromCache(filterVideoUrl(detailState.videoUrlOrPath));

      if (cacheVideoFile == null) {
        logger("getVideoDetail cacheVideoFile is null start download");
        await videoKit.open(detailState.videoUrlOrPath);
        downloadVideoCache(detailState.videoUrlOrPath);
      } else {
        detailState.videoUrlOrPath = cacheVideoFile.file.path;
        logger("getVideoDetail cacheVideoFile is not null,use cache path=$detailState.videoUrlOrPath");
        await videoKit.open(detailState.videoUrlOrPath);
      }
    }
  }

  void _uploadVideoInBackground(String videoPath) async {
    logger("开始后台上传视频文件: $videoPath");
    final url = await OssUtil().uploadUserPrivateVideoFile(videoPath);

    if (url != null) {
      logger("视频上传成功: $url");
      "视频上传成功！".toast;
      Net.getRestClient().updateUserLocalResource({
        'resourceId': detailState.localDetailResp!.resourceId,
        'resourceType': detailState.localDetailResp!.resourceType,
        'videoUrl': url,
      });
    } else {
      "视频上传失败，请重试".toast;
    }
  }

  /// 初始化播放器监听器
  void _initPlayerListeners() {
    logger("$moduleName: 初始化播放器监听器");

    // 播放状态监听
    addSubscription(videoKit.playing.listen((playing) {
      _onPlayingStateChanged(playing);
    }));

    // 播放位置监听
    addSubscription(videoKit.getPlayer.stream.position.listen((duration) {
      _onPositionChanged(duration);
    }));

    // 总时长监听
    addSubscription(videoKit.getPlayer.stream.duration.listen((duration) {
      logger("$moduleName: duration changed to $duration");
      _onDurationChanged(duration);
    }));

    // LS模式暂停监听
    addSubscription(videoKit.pausedInLsMode.listen((paused) {
      _onPausedInLsModeChanged(paused);
    }));
  }

  /// 处理播放状态变化
  void _onPlayingStateChanged(bool playing) {
    dataModule.onPlay(playing);
  }

  /// 处理播放位置变化
  void _onPositionChanged(Duration duration) {
    if (detailState.canChangeSeekBarRatio) {
      detailState.seekBarRatio.value = detailState.totalDuration.value == 0 ? 0 : duration.inMilliseconds / detailState.totalDuration.value;
    }
  }

  /// 处理总时长变化
  void _onDurationChanged(Duration? duration) {
    if (duration != null) {
      detailState.totalDuration.value = duration.inMilliseconds;
    }
  }

  /// 处理LS模式暂停状态变化
  void _onPausedInLsModeChanged(bool paused) {
    logger("$moduleName: LS mode paused state changed to $paused");
    if (paused && Config().playerConfig.autoRecord) {
      if (DateTime.now().millisecondsSinceEpoch - videoKit.onHorizontalDragEndTime < 1000) {
        logger("$moduleName: close autoRecord cause time offset < 1000");
        videoKit.onHorizontalDragEndTime = 0;
        return;
      }
      // 在这里可以触发自动录音逻辑
      // speechEvaluationModule.startRecording();
    }
  }

  /// 检查视频文件
  Future<void> checkVideo(String videoPath) async {
    if (detailState.isLocalVideo) {
      logger("$moduleName: checking video at path: $videoPath");
      await controller.playbackModule.checkVideoResolution(videoPath);
      await videoKit.open(detailState.videoUrlOrPath);
    }
  }

  /// 播放录音时暂停视频
  Future<void> playRecord() async {
    logger("$moduleName: pausing video for record playback");
    videoKit.pause();
    await speechEvaluationModule.playRecord();
  }

  /// 处理进度条变化
  Future<void> onSliderChange(double ratio) async {
    logger("$moduleName: slider change called with ratio: $ratio");
    detailState.canChangeSeekBarRatio = false;

    // 在LS模式下，停止录音和播放
    if (videoKit.openLsMode.value) {
      if (speechEvaluationModule.recordingInLsMode.value) {
        logger("$moduleName: stopping recording in LS mode");
        speechEvaluationModule.recognitionService.stop();
        speechEvaluationModule.recordingInLsMode.value = false;
      }
      speechEvaluationModule.recordSoundPlayer.stopPlayer();
    }

    // 计算目标位置
    var targetPosition = Duration(milliseconds: (ratio * detailState.totalDuration.value).toInt());
    logger("$moduleName: seeking to position: ${targetPosition.inMilliseconds}ms");

    // 在LS模式下，先处理跳过逻辑再seek
    if (videoKit.openLsMode.value) {
      // 根据目标位置找到对应的字幕索引
      var subtitle = videoKit.subtitleController?.findClosestSubtitleForward(targetPosition);
      if (subtitle != null) {
        logger("$moduleName: found subtitle at index ${subtitle.subtitleIndex} for target position");

        // 先设置当前索引
        videoKit.currentSubtitleIndex.value = subtitle.subtitleIndex;

        // 处理跳过逻辑
        var result = await videoKit.handleSkipLogicForCurrentPosition(showToast: true);

        if (result) {
          // 如果发生了跳转，使用跳转后的位置
          var validIndex = videoKit.currentSubtitleIndex.value;
          var validPosition = videoKit.subtitles[validIndex].start;
          logger("$moduleName: skipping to valid index $validIndex, position: ${validPosition.inMilliseconds}ms");
          await videoKit.seek(validPosition);
        } else {
          // 没有跳转，使用原始位置
          await videoKit.seek(targetPosition);
        }
      } else {
        // 没找到字幕，使用原始位置
        await videoKit.seek(targetPosition);
      }
    } else {
      // 非LS模式，直接seek
      await videoKit.seek(targetPosition);
    }

    // 处理seek结束后的状态重置
    await videoKit.onVideoSeekChangeEnd();

    detailState.canChangeSeekBarRatio = true;
    logger("$moduleName: slider change completed");
  }

  /// 处理字幕列表点击 - 使用简化的统一入口
  void onSubTitleListClick(int index) {
    logger("$moduleName: subtitle list clicked at index: $index");

    if (videoKit.openLsMode.value) {
      // LS模式下使用统一入口
      videoKit.playSubtitleInLsMode(index, fromUserAction: true);
    } else {
      // 非LS模式下使用原有逻辑
      videoKit.seekBySubtitleIndex(index);
      videoKit.play();
    }
  }

  /// 返回键拦截处理（处理全屏模式）
  bool interceptorPop(bool stopDefaultButtonEvent, RouteInfo info) {
    if (Get.context?.isLandscape == true) {
      videoKit.toggleFullscreen();
      return true;
    }
    return false;
  }

  @override
  void onVisibilityLost() {
    logger("$moduleName: visibility lost, pausing video");
    videoKit.pause();
    speechEvaluationModule.recognitionService.stop();
    speechEvaluationModule.recordSoundPlayer.stopPlayer();
    speechEvaluationModule.recordingInLsMode.value = false;
  }

  @override
  void onVisibilityGained() {
    logger("$moduleName: visibility gained");
    // 可以在这里添加恢复播放的逻辑，如果需要的话
  }

  @override
  void didChangeMetrics() {
    logger("$moduleName: metrics changed");
    // 播放器相关的屏幕变化处理已经在controller中处理
  }

  /// 关闭播放器
  Future<void> closePlayer() async {
    logger("$moduleName: closing player");
    videoKit.exitNativeFullscreen();
    // 这里可以添加其他关闭逻辑
    videoKit.destory();
  }
}
