import 'dart:async';
import 'package:lsenglish/app/modules/detail/controllers/detail_controller.dart';
import 'package:lsenglish/app/modules/detail/interfaces/detail_module_interface.dart';
import 'package:lsenglish/model/local_detail_resp/resource_detail_resp.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/video/media_player.dart';

import '../interfaces/detail_state.dart';
import 'collection_module.dart';
import 'data_module.dart';
import 'note_module.dart';
import 'player_module.dart';
import 'speech_evaluation_module.dart';
import 'subtitle_module.dart';

/// 基础模块类，提供通用的功能和状态管理
abstract class BaseModule extends DetailModuleInterface {
  final String _moduleName;
  final List<StreamSubscription> _subscriptions = [];
  bool _isInitialized = false;
  bool _isDisposed = false;

  BaseModule(this._moduleName);

  @override
  String get moduleName => _moduleName;

  bool get isInitialized => _isInitialized;

  bool get isDisposed => _isDisposed;

  late final DetailController controller;

  void setController(DetailController controller) {
    this.controller = controller;
  }

  DetailState get detailState => controller.detailState;
  MediaPlayer get videoKit => detailState.videoKit;
  ResourceDetailResp? get localDetailResp => detailState.localDetailResp;

  @override
  Future<void> initialize() async {
    if (_isInitialized) {
      logger("$moduleName: 模块已经初始化");
      return;
    }

    if (_isDisposed) {
      logger("$moduleName: 模块已销毁，无法初始化");
      return;
    }

    try {
      logger("$moduleName: 开始初始化");
      await onInitialize();
      _isInitialized = true;
      logger("$moduleName: 初始化完成");
    } catch (e) {
      logger("$moduleName: 初始化失败 - $e");
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    if (_isDisposed) {
      logger("$moduleName: 模块已经销毁");
      return;
    }

    try {
      logger("$moduleName: 开始销毁");

      // 取消所有订阅
      for (final subscription in _subscriptions) {
        await subscription.cancel();
      }
      _subscriptions.clear();

      // 调用子类的销毁方法
      await onDispose();

      _isDisposed = true;
      _isInitialized = false;
      logger("$moduleName: 销毁完成");
    } catch (e) {
      logger("$moduleName: 销毁失败 - $e");
      rethrow;
    }
  }

  /// 子类需要实现的初始化方法
  Future<void> onInitialize();

  /// 子类需要实现的销毁方法
  Future<void> onDispose();

  /// 添加订阅，自动管理生命周期
  void addSubscription(StreamSubscription subscription) {
    if (!_isDisposed) {
      _subscriptions.add(subscription);
    } else {
      subscription.cancel();
    }
  }

  /// 移除订阅
  void removeSubscription(StreamSubscription subscription) {
    _subscriptions.remove(subscription);
  }

  /// 安全地执行异步操作
  Future<T?> safeExecute<T>(Future<T> Function() operation, {String? operationName}) async {
    if (_isDisposed) {
      logger("$moduleName: 模块已销毁，跳过操作 ${operationName ?? 'unknown'}");
      return null;
    }

    try {
      return await operation();
    } catch (e) {
      logger("$moduleName: 操作 ${operationName ?? 'unknown'} 失败 - $e");
      return null;
    }
  }

  /// 安全地执行同步操作
  T? safeExecuteSync<T>(T Function() operation, {String? operationName}) {
    if (_isDisposed) {
      logger("$moduleName: 模块已销毁，跳过操作 ${operationName ?? 'unknown'}");
      return null;
    }

    try {
      return operation();
    } catch (e) {
      logger("$moduleName: 操作 ${operationName ?? 'unknown'} 失败 - $e");
      return null;
    }
  }

  /// 检查模块状态
  void checkState() {
    if (_isDisposed) {
      throw StateError("$moduleName: 模块已销毁");
    }
    if (!_isInitialized) {
      throw StateError("$moduleName: 模块未初始化");
    }
  }

  /// 获取订阅数量
  int get subscriptionCount => _subscriptions.length;

  /// 获取模块状态信息
  @override
  Map<String, dynamic> get stateInfo => {
        'moduleName': moduleName,
        'isInitialized': _isInitialized,
        'isDisposed': _isDisposed,
        'subscriptionCount': subscriptionCount,
      };

  DataModule get dataModule => controller.dataModule;
  PlayerModule get playerModule => controller.playerModule;
  SubtitleModule get subtitleModule => controller.subtitleModule;
  NoteModule get noteModule => controller.noteModule;
  SpeechEvaluationModule get speechEvaluationModule => controller.speechEvaluationModule;
  CollectionModule get collectionModule => controller.collectionModule;
}

/// 默认模块实现，提供所有抽象方法的空实现
/// 其他模块可以继承此类而不是直接继承BaseModule，这样添加新方法时不会强制实现
class DefaultModule extends BaseModule {
  DefaultModule(String moduleName) : super(moduleName);

  @override
  Future<void> onInitialize() async {
    // 默认空实现
  }

  @override
  Future<void> onDispose() async {
    // 默认空实现
  }

  @override
  Future<void> onFetchDetailData(Exception? error) async {
    // 默认空实现
  }

  /// 实现新增的接口方法，子类可以选择性重写
  @override
  void onVisibilityLost() {
    // 默认空实现，子类可以重写
  }

  @override
  void onVisibilityGained() {
    // 默认空实现，子类可以重写
  }

  @override
  void didChangeMetrics() {
    // 默认空实现，子类可以重写
  }
}
