import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/modules/detail/modules/base_module.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/model/local_detail_resp/resource_detail_resp.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/obs.dart';
import 'package:lsenglish/utils/sp.dart';
import 'package:lsenglish/utils/subtitle.dart';
import 'package:lsenglish/utils/subtitle/subtitle.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

/// 字幕模块实现
class SubtitleModule extends DefaultModule {
  // 字幕占位符基准显示状态 - 用户通过菜单设置的基础值
  final RxBool _baseShowSubtitlePlaceholder = true.obs;

  // 当前字幕占位符显示状态 - 可能被录音过程临时修改
  final RxBool _currentShowSubtitlePlaceholder = true.obs;

  // 标记是否应该在下一次字幕切换时恢复基准值
  bool _shouldRestoreOnSubtitleChange = false;

  SubtitleModule() : super('SubtitleModule');

  /// 获取字幕占位符基准显示状态
  RxBool get baseShowSubtitlePlaceholder => _baseShowSubtitlePlaceholder;

  /// 获取当前字幕占位符显示状态（对外暴露的接口）
  RxBool get showSubtitlePlaceholder => _currentShowSubtitlePlaceholder;

  /// 设置是否应该在下一次字幕切换时恢复基准值
  set shouldRestoreOnSubtitleChange(bool value) {
    _shouldRestoreOnSubtitleChange = value;
  }

  /// 获取是否应该在下一次字幕切换时恢复基准值
  bool get shouldRestoreOnSubtitleChange => _shouldRestoreOnSubtitleChange;

  @override
  Future<void> onInitialize() async {
    logger("$moduleName: 初始化字幕模块");

    // 监听字幕切换，当字幕切换时恢复基准值
    addSubscription(videoKit.currentSubtitleIndex.listen((index) {
      // 当字幕切换时，如果标记为需要恢复，则恢复基准值
      if (_shouldRestoreOnSubtitleChange) {
        _currentShowSubtitlePlaceholder.value = _baseShowSubtitlePlaceholder.value;
        _shouldRestoreOnSubtitleChange = false; // 重置标记
        logger("$moduleName: 字幕切换，恢复基准值: ${_baseShowSubtitlePlaceholder.value}");
      }
    }));
    ObsUtil().uploadSubtitle.listen((v) async {
      logger("receive uploadSubtitle obs");
      detailState.loadingSubtitle.value = true;
      detailState.localDetailResp = v;
      videoKit.finishSeekByHistory = false;
      videoKit.positionInit = videoKit.currentPositionInMilliseconds;
      await loadSubtitle();
      detailState.loadingSubtitle.value = false;
    });
  }

  @override
  Future<void> onFetchDetailData(Exception? error) async {
    if (error != null) {
      loadSubtitleWhenError(error.toString());
      return;
    }
    await loadSubtitle();
  }

  //处理和字幕相关的逻辑
  void relationSubtitle2IndexMap() {
    logger("SubtitleModule processRelationSubtitle");

    // 笔记现在由笔记模块处理，这里只处理收藏
    if (detailState.localDetailResp!.sentenceCollects != null) {
      collectionModule.loadCollectionsFromServer(detailState.localDetailResp!.sentenceCollects!);
    }
    videoKit.skipList = getIndexsFromIntervalList(detailState.localDetailResp?.skipList ?? [], videoKit.subtitles);
    logger("SubtitleModule processRelationSubtitle videoKit.skipList=${videoKit.skipList}");
  }

  @override
  Future<void> onDispose() async {
    logger("$moduleName: 销毁字幕模块");
  }

  /// 加载字幕
  Future<void> loadSubtitle() async {
    return safeExecute(() async {
      logger("$moduleName: 开始加载字幕");

      if (detailState.localDetailResp == null) {
        logger("$moduleName: 资源详情为空，无法加载字幕");
        return;
      }

      var subtitlePath = "";
      subtitlePath = detailState.localDetailResp!.subtitleUrl!;
      logger("$moduleName: 从远程获取字幕路径: $subtitlePath");

      if (detailState.isLocalVideo && subtitlePath.isEmpty && detailState.localDetailResp!.nativeLangSubtitleUrl != "") {
        // 说明没有从服务端获取到字幕文件 默认使用历史保存的
        var localHistoryModel = await SPUtil().getHistory(detailState.videoUrlOrPath);
        logger("$moduleName: 本地历史模型=${jsonEncode(localHistoryModel)}");
        if (localHistoryModel == null || localHistoryModel.subtitleLocalPath == null || localHistoryModel.subtitleLocalPath?.isEmpty == true) {
          SPUtil().saveHistory(detailState.videoUrlOrPath);
          logger("$moduleName: 本地历史模型为空");
          subtitlePath = await videoKit.getSubtitleByVideo(detailState.videoUrlOrPath);
        } else {
          subtitlePath = localHistoryModel.subtitleLocalPath ?? "";
        }
      }

      logger("$moduleName: 最终字幕路径: $subtitlePath");

      if (shouldUseDoubleSubtitle(detailState.localDetailResp)) {
        logger(
            "$moduleName: 远程资源加载目标字幕 ${detailState.localDetailResp!.originSubtitleUrl} 和原生字幕 ${detailState.localDetailResp!.nativeLangSubtitleUrl}");
        await videoKit.loadSubtitles(detailState.localDetailResp!.originSubtitleUrl ?? "",
            nativeSubtitlePath: detailState.localDetailResp!.nativeLangSubtitleUrl ?? "");
      } else {
        await videoKit.loadSubtitles(subtitlePath);
      }

      logger("$moduleName: 字幕加载完成,开始处理跳过收藏数据解析");
      relationSubtitle2IndexMap();
      logger("$moduleName: 字幕加载完成,开始处理历史跳转");
      logger("loadSubtitles await _videoLoadFinish");
      await Future.wait([
        videoKit.videoLoadFinish.future,
      ]);
      logger("SubtitleModule loadSubtitles seekByHistory = ${videoKit.positionInit}");
      await videoKit.seekByHistory(videoKit.positionInit);
      logger("SubtitleModule loadSubtitles play after seekByHistory");
      videoKit.play();
    }, operationName: 'loadSubtitle');
  }

  int getCurrentSubtitleIndex() {
    if (detailState.isLandscape.value) {
      return videoKit.currentSubtitleIndex.value;
    }
    if (!videoKit.openLsMode.value) {
      return videoKit.currentSubtitleIndex.value;
    }
    if (detailState.pageController.hasClients) {
      return detailState.pageController.page?.toInt() ?? 0;
    }
    return 0;
  }

  /// 从错误中加载字幕
  Future<void> loadSubtitleWhenError(String error) async {
    return safeExecute(() async {
      detailState.loadingSubtitle.value = false;
      logger("$moduleName: 字幕加载错误=$error");
      videoKit.play();
    }, operationName: 'loadSubtitleWhenError');
  }

  /// 编辑字幕
  Future<void> editSubtitle() async {
    return safeExecute(() async {
      if (detailState.localDetailResp == null) {
        Get.toNamed(Routes.LOGIN);
        return;
      }

      var result = await Get.toNamed(Routes.SUBTITLE_EDIT, arguments: {
        'subtitleUrl': detailState.localDetailResp!.subtitleUrl,
        'resourceId': detailState.localDetailResp!.resourceId,
        'skipList': detailState.localDetailResp!.skipList,
        'resourceType': detailState.localDetailResp!.resourceType,
        'index': getCurrentSubtitleIndex(),
        'nativeLangSubtitleUrl': detailState.localDetailResp?.nativeLangSubtitleUrl,
        'originSubtitleUrl': detailState.localDetailResp?.originSubtitleUrl,
      });
      if (result == true) {
        await loadSubtitle();
      }
    }, operationName: 'editSubtitle');
  }

  /// 搜索字幕
  Future<void> searchSubtitle() async {
    return safeExecute(() async {
      var subtitleAddResult = await Get.toNamed(Routes.SUBTITLE_ADD, arguments: {'videoPath': detailState.videoUrlOrPath});
      if (subtitleAddResult != null) {
        var subtitlePath = subtitleAddResult['subtitlePath'];
        logger("$moduleName: 字幕搜索结果路径=$subtitlePath");
        await addSubtitle(subtitlePath);
      }
    }, operationName: 'searchSubtitle');
  }

  /// 从视频中获取字幕
  Future<void> getSubtitleByVideo() async {
    return safeExecute(() async {
      var subtitlePath = await videoKit.getSubtitleByVideo(detailState.videoUrlOrPath);
      if (subtitlePath.isEmpty) {
        "暂时无法解析出目录".toast;
        return;
      }
      await addSubtitle(subtitlePath);
    }, operationName: 'getSubtitleByVideo');
  }

  /// 添加字幕
  Future<void> addSubtitle(String subtitleUrl) async {
    return safeExecute(() async {
      SPUtil().saveHistory(detailState.videoUrlOrPath, subtitleLocalPath: subtitleUrl);
      videoKit.resetLsModeIndex(needPlay: false);
      await videoKit.loadSubtitles(subtitleUrl);
      logger("$moduleName: 加载字幕并上传 $subtitleUrl");
      await SubtitleUtil().uploadSubtitle(
        localDetailResp?.resourceId,
        localDetailResp?.resourceType,
        subtitleUrlCanUseDirect: subtitleUrl,
        autoAddSkipWhenSubtitleTargetNone: true,
      );
    }, operationName: 'addSubtitle');
  }

  /// 切换字幕占位符显示 - 用户主动控制，只改变基准值
  void toggleSubtitlePlaceholder() {
    safeExecuteSync(() {
      _baseShowSubtitlePlaceholder.value = !_baseShowSubtitlePlaceholder.value;
      _currentShowSubtitlePlaceholder.value = _baseShowSubtitlePlaceholder.value;
      // 用户手动操作时，清除恢复标记
      _shouldRestoreOnSubtitleChange = false;
      logger("$moduleName: 用户主动切换基准值: ${_baseShowSubtitlePlaceholder.value}");
    }, operationName: 'toggleSubtitlePlaceholder');
  }

  /// 录音开始时隐藏字幕
  void hideSubtitleOnRecordStart() {
    safeExecuteSync(() {
      _currentShowSubtitlePlaceholder.value = true; // 显示占位符 = 隐藏字幕
      logger("$moduleName: 录音开始时隐藏字幕");
    }, operationName: 'hideSubtitleOnRecordStart');
  }

  /// 录音结束后根据配置决定是否显示字幕
  void showSubtitleAfterRecordEnd() {
    safeExecuteSync(() {
      // 检查配置是否允许录音结束后显示字幕
      if (Config().playerConfig.showSubtitleWhenRecordEnd) {
        _currentShowSubtitlePlaceholder.value = false; // 隐藏占位符 = 显示字幕
        _shouldRestoreOnSubtitleChange = true;
        logger("$moduleName: 录音结束后自动显示字幕");
      } else {
        // 如果配置不允许自动显示，则恢复基准值
        _currentShowSubtitlePlaceholder.value = _baseShowSubtitlePlaceholder.value;
        logger("$moduleName: 录音结束后恢复基准值: ${_baseShowSubtitlePlaceholder.value}");
      }
    }, operationName: 'showSubtitleAfterRecordEnd');
  }

  /// 跳转到指定字幕
  void jumpToSubtitle(int index) {
    safeExecuteSync(() {
      if (detailState.isLandscape.value == true) {
        return;
      }
      if (index >= 0 && index < videoKit.subtitles.length) {
        if (!detailState.pageController.hasClients) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            detailState.pageController.jumpToPage(index);
          });
        } else {
          detailState.pageController.jumpToPage(index);
        }
      }
    }, operationName: 'jumpToSubtitle');
  }

  /// 跳转到上一个字幕
  void jumpToPreviousSubtitle() {
    safeExecuteSync(() {
      videoKit.skipFindReverse = true;
      videoKit.preSubtitle();
      videoKit.play();
    }, operationName: 'jumpToPreviousSubtitle');
  }

  /// 跳转到下一个字幕
  void jumpToNextSubtitle() {
    safeExecuteSync(() {
      videoKit.skipFindReverse = false;
      videoKit.nextSubtitle();
      videoKit.play();
    }, operationName: 'jumpToNextSubtitle');
  }

  /// 字幕列表滚动到中心
  void scrollToCenter() {
    safeExecuteSync(() {
      if (detailState.isLandscape.value == true) {
        return;
      }
      // 暂时跳过自动滚动检查，因为allowAutoScroll是私有变量
      // TODO: 在DetailController中添加公共getter方法
      detailState.itemScrollController.scrollToIndex(detailState.currentPage.value, preferPosition: AutoScrollPosition.middle);
    }, operationName: 'scrollToCenter');
  }

  /// 根据时间范围查找字幕索引
  int findSubtitleIndexByTime(int? startTime, int? endTime) {
    if (startTime == null || endTime == null) return -1;

    for (int i = 0; i < videoKit.subtitles.length; i++) {
      final subtitle = videoKit.subtitles[i];
      if (subtitle.start.inMilliseconds == startTime && subtitle.end.inMilliseconds == endTime) {
        return i;
      }
    }
    return -1;
  }

  /// 获取字幕列表
  List<Subtitle> get subtitles => videoKit.subtitles;

  /// 获取字幕数量
  int get subtitleCount => subtitles.length;

  /// 获取当前字幕
  Subtitle? get currentSubtitle {
    final index = getCurrentSubtitleIndex();
    if (index >= 0 && index < subtitles.length) {
      return subtitles[index];
    }
    return null;
  }

  /// 检查是否有字幕
  bool get hasSubtitles => subtitles.isNotEmpty;

  /// 获取字幕路径
  String? get subtitlePath => detailState.localDetailResp?.subtitleUrl;

  /// 获取原生语言字幕路径
  String? get nativeSubtitlePath => detailState.localDetailResp?.nativeLangSubtitleUrl;

  /// 获取原始字幕路径
  String? get originSubtitlePath => detailState.localDetailResp?.originSubtitleUrl;

  /// 检查是否应该使用双字幕
  bool shouldUseDoubleSubtitle(ResourceDetailResp? detailResp) {
    return detailResp?.originSubtitleUrl != null &&
        detailResp!.originSubtitleUrl!.isNotEmpty &&
        detailResp.nativeLangSubtitleUrl != null &&
        detailResp.nativeLangSubtitleUrl!.isNotEmpty;
  }

  /// 获取模块状态信息
  @override
  Map<String, dynamic> get stateInfo {
    final baseInfo = super.stateInfo;
    baseInfo.addAll({
      'subtitleCount': subtitleCount,
      'hasSubtitles': hasSubtitles,
      'currentSubtitleIndex': getCurrentSubtitleIndex(),
      'subtitlePath': subtitlePath,
      'nativeSubtitlePath': nativeSubtitlePath,
      'originSubtitlePath': originSubtitlePath,
    });
    return baseInfo;
  }
}
