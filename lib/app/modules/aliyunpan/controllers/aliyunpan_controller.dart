import 'dart:convert';

import 'package:aliyunpan_flutter_sdk_auth/aliyunpan_flutter_sdk_auth.dart';
import 'package:aliyunpan_sdk/aliyunpan_sdk.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/modules/aliyunpan/views/aliyun_webview_page.dart';
import 'package:lsenglish/net/net.dart';
import 'package:get_storage/get_storage.dart';

class AliyunpanController extends GetxController {
  late final AliyunpanClient _aliyunpanClient;
  final drives = <Drive>[].obs;
  final isLoading = false.obs;

  // 添加 getter 来访问 _aliyunpanClient
  AliyunpanClient get aliyunpanClient => _aliyunpanClient;

  // 添加历史记录
  final _history = <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initClient();
    initAliyunPan();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void _initClient() {
    _aliyunpanClient = AliyunpanClient(
      "0077298293394e1fa771f0b0e005800f",
      refreshTokenFunction: refreshToken,
      debug: true,
      onTokenChange: (token) => GetStorage().write('key_token', jsonEncode(token)),
    );
  }

  Future<void> initAliyunPan() async {
    try {
      final tokenStr = GetStorage().read('key_token');
      debugPrint("tokenStr = $tokenStr");

      if (tokenStr != null) {
        try {
          final token = Token.fromJson(jsonDecode(tokenStr));
          // 直接设置 token
          _aliyunpanClient.token = token;
          await getDrives();
          return;
        } catch (e) {
          debugPrint('已存储的 token 无效: $e');
        }
      }
      aliyunpanAuth();
    } catch (e) {
      debugPrint('初始化阿里云盘失败: $e');
      aliyunpanAuth();
    }
  }

  Future<void> aliyunpanAuth() async {
    // token 不存在或已过期，进行授权
    final credentials = FlutterCredentials.server(
      bundleId: "com.seedtu.ls",
      authenticator: FlutterAuthenticator(
        (subscription) {},
        (uri, accept) async {
          final result = await Navigator.push<String>(
            Get.context!,
            MaterialPageRoute(
              builder: (context) => AliyunWebViewPage(
                uri: uri,
                accept: accept,
              ),
            ),
          );
          return result;
        },
        forceSSO: false,
      ),
      requestTokenFunction: requestToken,
    );

    await _aliyunpanClient.authorize(credentials);
    await getDrives();
  }

  Future<void> getDrives() async {
    try {
      isLoading.value = true;
      final result = await _aliyunpanClient.send(const GetDrive());
      drives.value = [result];

      // 清空历史记录并添加根目录
      _history.clear();
      _history.add({
        'driveId': '',
        'parentFileId': 'root',
        'files': [],
      });
    } catch (e) {
      debugPrint('获取驱动器列表失败: $e');
      aliyunpanAuth();
    } finally {
      isLoading.value = false;
    }
  }

  Future<Token> refreshToken(refreshToken) {
    return Net.getDio().post('https://openapi.alipan.com/oauth/access_token', data: {
      'client_id': "0077298293394e1fa771f0b0e005800f",
      'client_secret': "72d212849b1c4068a6358b18c3e1f8e6",
      'grant_type': 'refresh_token',
      'refresh_token': refreshToken,
    }).then((value) => Token.fromJson(value.data));
  }

  Future<Token> requestToken(String code) async {
    return Net.getDio().post('https://openapi.alipan.com/oauth/access_token', data: {
      'client_id': "0077298293394e1fa771f0b0e005800f",
      'client_secret': "72d212849b1c4068a6358b18c3e1f8e6",
      'grant_type': 'authorization_code',
      'code': code,
    }).then((value) => Token.fromJson(value.data));
  }
}
