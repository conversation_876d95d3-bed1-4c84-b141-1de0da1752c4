import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';

import 'package:lsenglish/base/loading_getview.dart';
import 'package:lsenglish/utils/dialog.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/utils/time.dart';
import 'package:lsenglish/widgets/tab_indicator_styler/src/indicators/rectangular_indicator.dart';

import '../controllers/datacenter_controller.dart';
import 'total_chart_page.dart';

class DatacenterView extends LoadingGetView<DatacenterController> {
  const DatacenterView({super.key});
  @override
  Widget buildSuccess(BuildContext context) {
    return Scaffold(
      backgroundColor: Get.theme.appBarTheme.surfaceTintColor,
      appBar: AppBar(title: const Text("学习数据")),
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.whs),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Column(
                  children: [
                    Theme(
                      data: Theme.of(context).copyWith(tabBarTheme: const TabBarThemeData(labelPadding: EdgeInsets.zero)),
                      child: Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.all(Radius.circular(8)),
                        ),
                        height: 35.whs,
                        child: TabBar(
                          controller: controller.tabController,
                          tabs: ["日", "周", "月", "年"].map((label) => Text(label)).toList(),
                          labelColor: Colors.black,
                          labelStyle: TextStyle(fontSize: 15.whs, fontWeight: FontWeight.w600),
                          unselectedLabelStyle: TextStyle(fontSize: 15.whs, fontWeight: FontWeight.w500),
                          unselectedLabelColor: const Color(0xff999999),
                          dividerColor: Colors.transparent,
                          indicatorSize: TabBarIndicatorSize.tab,
                          indicator: RectangularIndicator(
                            verticalPadding: 2.whs,
                            horizontalPadding: 2.whs,
                            radius: 8,
                            strokeWidth: 2,
                            color: const Color(0xffDFE3E8),
                            selectBackgroundColor: Colors.white,
                            paintingStyle: PaintingStyle.stroke,
                          ),
                        ),
                      ),
                    ),
                    Gap(10.whs),
                    Obx(() => TotalChartPageWidget(
                          tabType: controller.tabType.value,
                          pageIndexCallback: (index) {
                            controller.onChartPageIndexChange(index);
                          },
                        )),
                  ],
                ),
                Gap(16.whs),
                Text("学习", style: Get.textTheme.headlineLarge),
                Gap(16.whs),
                Obx(() => Visibility(visible: controller.dataCenterHomeResp.value.episodes != null, child: _buildTrend())),
                Obx(() => Visibility(visible: controller.dataCenterHomeResp.value.total != null, child: _buildTotal())),
                Gap(40.whs),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTrend() {
    return Obx(
      () => ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: controller.dataCenterHomeResp.value.episodes?.length ?? 0,
        itemBuilder: (BuildContext context, int index) {
          final episode = controller.dataCenterHomeResp.value.episodes?[index];
          
          return GestureDetector(
            onTap: () {
              Get.toNamed(Routes.EPISODEDATA, arguments: {
                'resourceId': episode?.resourceId,
                'resourceType': episode?.resourceType,
                'episodeName': episode?.episodeName,
              });
            },
            child: Padding(
              padding: EdgeInsets.only(
                  top: index == 0 ? 0 : 6.whs, bottom: index == (controller.dataCenterHomeResp.value.episodes?.length ?? 0) - 1 ? 12.whs : 6.whs),
              child: Container(
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.whs),
                ),
                padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 12.whs),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              showChangeResourceNameDialog(
                                  episode?.resourceId,
                                  episode?.resourceType,
                                  episode?.episodeName);
                            },
                            child: Text(
                              episode?.episodeName ?? "",
                              style: Get.textTheme.titleLarge,
                            ),
                          ),
                        ),
                        Icon(Icons.arrow_forward_ios_rounded, size: 24.whs, color: Colors.grey)
                      ],
                    ),
                    Gap(16.whs),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 句子数量
                        Flexible(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                (episode?.currentSentences ?? 0).toString(),
                                style: TextStyle(
                                  fontSize: 24.whs, 
                                  color: (episode?.currentSentences ?? 0) > (episode?.targetSentences ?? 0) 
                                      ? Get.theme.primaryColor 
                                      : Colors.black, 
                                  fontWeight: FontWeight.bold
                                ),
                              ),
                              Text(
                                "${episode?.targetSentences.toString() ?? "--"} sent.",
                                style: TextStyle(fontSize: 12.whs, color: Colors.grey, decoration: TextDecoration.underline),
                              ),
                            ],
                          ),
                        ),
                        // 平均分数
                        Flexible(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                episode?.averageScore?.toStringAsFixed(0) ?? "--",
                                style: TextStyle(fontSize: 24.whs, color: Colors.black, fontWeight: FontWeight.bold),
                              ),
                              Text(
                                "avg.score",
                                style: TextStyle(fontSize: 12.whs, color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                        // 分钟数
                        Flexible(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                millisecondsToMinutesString(episode?.totalLearnDuration ?? 0, precision: 0),
                                style: TextStyle(fontSize: 24.whs, color: Colors.black, fontWeight: FontWeight.bold),
                              ),
                              Text(
                                "min",
                                style: TextStyle(fontSize: 12.whs, color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                        // LS值
                        Flexible(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                episode?.currentLsTimes.toString() ?? "--",
                                style: TextStyle(fontSize: 24.whs, color: Colors.black, fontWeight: FontWeight.bold),
                              ),
                              Text(
                                "${episode?.targetLsTimes.toString() ?? "--"} LS",
                                style: TextStyle(fontSize: 12.whs, color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTotal() {
    var title = ["学习总时长", "学习总天数", "学习视频"];
    var unit = ["", "d", "个"];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text("累计", style: Get.textTheme.headlineLarge),
        Gap(12.whs),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3,
          itemBuilder: (BuildContext context, int index) {
            return Padding(
              padding: EdgeInsets.only(top: index == 0 ? 0 : 6.whs, bottom: index == 1 ? 12.whs : 6.whs),
              child: GestureDetector(
                onTap: () {
                  if (index == 2) {
                    Get.toNamed(Routes.EPISODELIST);
                  }
                },
                child: Container(
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16.whs),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 12.whs),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            title[index],
                            style: TextStyle(fontSize: 18.whs, color: Get.theme.primaryColor, fontWeight: FontWeight.bold),
                          ),
                          Icon(Icons.arrow_forward_ios_rounded, size: 24.whs, color: Colors.grey)
                        ],
                      ),
                      Gap(32.whs),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.baseline,
                        textBaseline: TextBaseline.alphabetic,
                        children: [
                          index == 0
                              ? Obx(
                                  () => RichText(
                                    text: formatMillisecondsSpan(controller.dataCenterHomeResp.value.total?.totalLearnDuration, numFontSize: 32),
                                  ),
                                )
                              : index == 1
                                  ? Obx(() => Text(
                                        controller.dataCenterHomeResp.value.total?.totalLearnDayTimes.toString() ?? "--",
                                        style: TextStyle(fontSize: 32.whs, color: Colors.black, fontWeight: FontWeight.bold),
                                      ))
                                  : Obx(() => Text(
                                        controller.dataCenterHomeResp.value.total?.totalLearnVideoSize.toString() ?? "--",
                                        style: TextStyle(fontSize: 32.whs, color: Colors.black, fontWeight: FontWeight.bold),
                                      )),
                          Text(
                            " ${unit[index]}",
                            style: TextStyle(fontSize: 16.whs, color: Colors.grey, fontWeight: FontWeight.bold),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
