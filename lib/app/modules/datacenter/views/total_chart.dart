import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/model/data_center_chart_resp/data_center_chart_data.dart';
import 'package:lsenglish/model/data_center_chart_resp/data_center_chart_resp.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/theme.dart';
import 'package:lsenglish/utils/chart.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/time.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

typedef SelectIndexCallback = void Function(int, int?);
typedef ChartDataCallback = void Function(DataCenterChartResp?);

class TotalChartWidget extends StatefulWidget {
  final int type;
  final SelectIndexCallback selectIndexCallback;
  final ChartDataCallback chartDataCallback;
  final int pageIndex;
  const TotalChartWidget({
    super.key,
    required this.type,
    required this.pageIndex,
    required this.selectIndexCallback,
    required this.chartDataCallback,
  });

  @override
  State<TotalChartWidget> createState() => _TotalChartWidgetState();
}

class _TotalChartWidgetState extends State<TotalChartWidget> {
  int? touchedGroupIndex;
  var chartData = <ChartCategoryData>[];
  var adjustMinY = 0.0;
  var adjustMaxY = 100.0;
  int? selectedIndex;
  CustomBarSeriesRenderer? _customBarSeriesRenderer;
  DataCenterChartResp? dataCenterChartResp;

  @override
  void initState() {
    super.initState();
    fetchChartData();
  }

  @override
  void dispose() {
    selectedIndex = null;
    widget.selectIndexCallback(widget.pageIndex, selectedIndex);
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant TotalChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.type != widget.type) {
      selectedIndex = null;
      widget.selectIndexCallback(widget.pageIndex, selectedIndex);
      _customBarSeriesRenderer?.updateNull();
      fetchChartData();
    }
  }

  void fetchChartData() {
    var timePair = getChartTimeRange(widget.type, widget.pageIndex);
    Net.getRestClient().dataEpisodeChart(widget.type, timePair.first ?? 0, timePair.second ?? 0).then((value) {
      if (value.data.datas == null || value.data.datas?.isEmpty == true) {
        return;
      }
      dataCenterChartResp = value.data;
      widget.chartDataCallback(value.data);
      var datas = value.data.datas!;
      var tempChartData = <ChartCategoryData>[];
      for (var i = 0; i < datas.length; i++) {
        tempChartData.add(ChartCategoryData(category: i.toString(), y: datas[i].duration != null ? millisecondsToMinutes(datas[i].duration!) : null));
      }
      chartData = tempChartData;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _handleAdjustY();
        });
      });
    });
  }

  void _handleAdjustY() {
    var maxY = chartData.where((element) => element.y != null).map((element) => element.y!).reduce((current, next) => current > next ? current : next)
        as double;
    var minY = chartData.where((element) => element.y != null).map((element) => element.y!).reduce((current, next) => current < next ? current : next)
        as double;
    var adjustYValues = calculateYAxisLabels(minY, maxY);
    setState(() {
      adjustMinY = adjustYValues.first;
      adjustMaxY = adjustYValues.second;
    });
    logger("adjustMinY = $adjustMinY   adjustMaxY = $adjustMaxY chartData size = ${chartData.length}");
  }

  DataCenterChartData? getMaxDurationData(List<DataCenterChartData> datas) {
    if (datas.isEmpty) return null;

    DataCenterChartData? maxData = datas[0];
    for (var data in datas) {
      if (data.duration != null && data.duration! > (maxData?.duration ?? 0)) {
        maxData = data;
      }
    }
    return maxData;
  }

  @override
  Widget build(BuildContext context) {
    return _buildChart();
  }

  SfCartesianChart _buildChart() {
    return SfCartesianChart(
      plotAreaBorderWidth: 1,
      plotAreaBorderColor: gray200,
      primaryXAxis: CategoryAxis(
        interval: getXInterval(),
        axisLine: const AxisLine(width: 0),
        majorTickLines: const MajorTickLines(size: 0),
        majorGridLines: MajorGridLines(width: 1, color: gray200, dashArray: const <double>[2, 2]),
        edgeLabelPlacement: EdgeLabelPlacement.shift,
        labelAlignment: getLabelAlignment(),
        axisLabelFormatter: (AxisLabelRenderDetails details) {
          return getXLabel(details);
        },
      ),
      primaryYAxis: NumericAxis(
        opposedPosition: true,
        axisLine: const AxisLine(width: 0),
        majorTickLines: const MajorTickLines(size: 0),
        majorGridLines: MajorGridLines(width: 1, color: gray200),
        interval: (adjustMaxY - adjustMinY) / 2,
        minimum: adjustMinY,
        maximum: adjustMaxY,
        labelAlignment: LabelAlignment.center,
        axisLabelFormatter: (details) {
          return ChartAxisLabel(
            details.text,
            const TextStyle(
              height: 2,
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: Color(0xff8D8C8C),
            ),
          );
        },
      ),
      series: getDefaultBarPanningSeries(),
      onChartTouchInteractionDown: (tapArgs) {},
      onChartTouchInteractionMove: (tapArgs) {},
      onChartTouchInteractionUp: (tapArgs) {
        var chartWidth = _customBarSeriesRenderer?.size.width ?? 0;
        var barCount = chartData.length;
        double barWidth = chartWidth / barCount;
        int clickBarIndex = (tapArgs.position.dx / barWidth).floor();
        int barIndex = clickBarIndex;
        var hasClickDataIndex = true;
        if (barIndex >= 0 && barIndex < barCount) {
          if (chartData[barIndex].y == null || chartData[barIndex].y == 0) {
            // 检查周围的四个条形
            var indicesToCheck = [barIndex - 1, barIndex + 1, barIndex - 2, barIndex + 2];
            var foundNonZeroIndex = indicesToCheck.firstWhere((index) {
              return index >= 0 && index < barCount && chartData[index].y != null && chartData[index].y != 0;
            }, orElse: () => -1);

            //判断selectedIndex为null 就是不显示的时候 点击其他区域时自动定位一下 以免点不到
            if (foundNonZeroIndex != -1 && selectedIndex == null) {
              barIndex = foundNonZeroIndex;
            } else {
              hasClickDataIndex = false;
            }
          }
        } else {
          hasClickDataIndex = false;
        }
        if (!hasClickDataIndex) {
          setState(() {
            selectedIndex = null;
            _customBarSeriesRenderer?.updateNull();
          });
        } else {
          setState(() {
            selectedIndex = barIndex;
            if (chartData[barIndex].y != 0) {
              _customBarSeriesRenderer?.updateData(
                selectedIndex,
                chartData.length,
                widget.type,
                dataCenterChartResp?.datas?[barIndex],
              );
            }
          });
        }
        widget.selectIndexCallback(widget.pageIndex, selectedIndex);
      },
    );
  }

  void getNearestBarIndex(Offset tapPosition, double chartWidth) {
    var barCount = chartData.length;
    double barWidth = chartWidth / barCount;
    int nearestIndex = -1;
    double minDistance = double.infinity;

    for (int i = 0; i < barCount; i++) {
      double barCenterX = (i + 0.5) * barWidth;
      double distance = (tapPosition.dx - barCenterX).abs();

      if (distance < minDistance) {
        minDistance = distance;
        nearestIndex = i;
      }
    }

    if (nearestIndex != -1) {
      logger("Nearest bar index: $nearestIndex");
    } else {
      logger("No bar found");
    }
  }

  List<ColumnSeries<ChartCategoryData, String>> getDefaultBarPanningSeries() {
    return <ColumnSeries<ChartCategoryData, String>>[
      ColumnSeries<ChartCategoryData, String>(
        animationDuration: 500,
        dataSource: chartData,
        color: Get.theme.primaryColor,
        width: 0.7,
        borderRadius: const BorderRadius.only(topLeft: Radius.circular(4), topRight: Radius.circular(4)),
        xValueMapper: (ChartCategoryData sales, _) => sales.category ?? "",
        yValueMapper: (ChartCategoryData sales, _) => sales.y,
        onCreateRenderer: (series) {
          _customBarSeriesRenderer = CustomBarSeriesRenderer();
          return _customBarSeriesRenderer!;
        },
      ),
    ];
  }

  double getXInterval() {
    //1 是tab的第一个
    if (widget.type == 1) {
      return 3;
    }
    if (widget.type == 3) {
      return 7;
    }
    return 1;
  }

  LabelAlignment getLabelAlignment() {
    if (widget.type == 1) {
      return LabelAlignment.start;
    }
    if (widget.type == 3) {
      return LabelAlignment.start;
    }
    return LabelAlignment.center;
  }

  ChartAxisLabel getXLabel(AxisLabelRenderDetails details) {
    if (widget.type == 1) {
      //日数据
      if (details.value == 0 || details.value.toInt() == 6 || details.value == 12.0 || details.value == 18.0) {
        return ChartAxisLabel("${details.value.toInt()}时", null);
      }
    } else if (widget.type == 2) {
      var weekStrs = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
      return ChartAxisLabel(weekStrs[details.value.toInt()], null);
    } else if (widget.type == 3) {
      if (details.value == 0 || details.value.toInt() == 7 || details.value == 14 || details.value == 21 || details.value == 28) {
        return ChartAxisLabel("${details.value.toInt() + 1}日", null);
      }
    } else if (widget.type == 4) {
      return ChartAxisLabel("${details.value.toInt() + 1}", null);
    }
    return ChartAxisLabel("", null);
  }
}

class CustomBarSeriesRenderer extends ColumnSeriesRenderer<ChartCategoryData, String> {
  int? clickIndex = -1;
  int totalBars = 0;
  int tabType = 1;
  DataCenterChartData? dataCenterChartData;
  void updateNull() {
    clickIndex = null;
  }

  void updateData(int? x, int totalBars, int tabType, DataCenterChartData? dataCenterChartData) {
    clickIndex = x;
    this.totalBars = totalBars;
    this.tabType = tabType;
    this.dataCenterChartData = dataCenterChartData;
    markNeedsPaint();
  }

  @override
  void onPaint(PaintingContext context, Offset offset) {
    if (clickIndex != null && clickIndex != -1 && dataCenterChartData != null) {
      double barWidth = size.width / (totalBars * 2);
      double centerX = (clickIndex! * 2 + 1) * barWidth;
      commonTooltipsPaint(context, centerX, offset, size, getTootipsTextSpan(tabType, dataCenterChartData!));
    }

    super.onPaint(context, offset);
  }
}
