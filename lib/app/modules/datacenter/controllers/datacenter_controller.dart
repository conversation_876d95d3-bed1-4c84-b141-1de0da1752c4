import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/utils/chart.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/login.dart';
import 'package:lsenglish/utils/obs.dart';
import 'package:lsenglish/net/net.dart';

import '../../../../base/loading_getxcontroller.dart';
import '../../../../model/data_center_home_resp/data_center_home_resp.dart';

class DatacenterController extends LoadingGetxcontroller with GetTickerProviderStateMixin {
  var dataCenterHomeResp = DataCenterHomeResp().obs;
  late TabController tabController;
  var tabType = 1.obs;
  var pageIndex = 0;
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(vsync: this, length: 4);
    tabController.addListener(_handleTabSelection);
    resetState();
    subscriptions.addAll([
      ObsUtil().updateDataCenter.listen((data) {
        logger("接收到数据中心刷新 $data");
        loadRemote();
      }),
      ObsUtil().loginStatus.listen(
        (v) {
          if (v && dataCenterHomeResp.value.total == null) {
            resetState();
          }
        },
      ),
      ObsUtil().renameLocalFileName.listen((v) {
        loadRemote();
      }),
    ]);
  }

  void _handleTabSelection() {
    if (tabController.indexIsChanging) {
      tabType.value = tabController.index + 1;
      loadRemote();
    }
  }

  void onTabClick() {
    if (dataCenterHomeResp.value.total == null) {
      resetState();
    }
  }

  @override
  void resetState() {
    super.resetState();
    loadRemote();
  }

  void loadRemote() {
    if (!isLogin()) {
      Get.offAllNamed(Routes.LOGIN);
      return;
    }
    var timePair = getChartTimeRange(tabType.value, pageIndex);
    Net.getRestClient().dataEpisodeHome(tabType.value, timePair.first ?? 0, timePair.second ?? 0).then((value) {
      dataCenterHomeResp.value = value.data;
      setSuccess();
    }).catchError((e) {
      logger("DatacenterController loadRemote error $e");
      setEmpty();
    });
  }

  void onChartPageIndexChange(int pageIndex) {
    if (this.pageIndex == pageIndex) {
      return;
    }
    this.pageIndex = pageIndex;
    debugPrint("onChartPageIndexChange pageIndex = $pageIndex");
    loadRemote();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
