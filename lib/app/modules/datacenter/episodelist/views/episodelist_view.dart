import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/base/loading_getview.dart';
import 'package:lsenglish/utils/dialog.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/utils/time.dart';

import '../controllers/episodelist_controller.dart';

class EpisodelistView extends LoadingGetView<EpisodelistController> {
  const EpisodelistView({super.key});
  @override
  Widget buildSuccess(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("累计学习剧集"),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.whs),
        child: Obx(
          () => ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.episodeList.length,
            itemBuilder: (BuildContext context, int index) {
              return GestureDetector(
                onTap: () {
                  Get.toNamed(Routes.EPISODEDATA, arguments: {
                    'resourceId': controller.episodeList[index].resourceId,
                    'resourceType': controller.episodeList[index].resourceType,
                    'episodeName': controller.episodeList[index].episodeName,
                  });
                },
                child: Padding(
                  padding: EdgeInsets.only(top: index == 0 ? 0 : 6.whs, bottom: index == (controller.episodeList.length) - 1 ? 12.whs : 6.whs),
                  child: Container(
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16.whs),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 12.whs),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  showChangeResourceNameDialog(controller.episodeList[index].resourceId, controller.episodeList[index].resourceType,
                                      controller.episodeList[index].episodeName);
                                },
                                child: Text(
                                  controller.episodeList[index].episodeName ?? "",
                                  style: TextStyle(fontSize: 18.whs, color: Get.theme.primaryColor, fontWeight: FontWeight.bold),
                                ),
                              ),
                            ),
                            Icon(Icons.arrow_forward_ios_rounded, size: 24.whs, color: Colors.grey)
                          ],
                        ),
                        Gap(32.whs),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.baseline,
                              textBaseline: TextBaseline.alphabetic,
                              children: [
                                Container(
                                  constraints: BoxConstraints(minWidth: 100.whs),
                                  child: RichText(
                                    text: formatMillisecondsSpan(controller.episodeList[index].totalLearnDuration, numFontSize: 32),
                                  ),
                                ),
                                Gap(8.whs),
                                Text(
                                  ((controller.episodeList[index].currentLsTimes?.toInt() ?? 1) - 1).toString(),
                                  style: TextStyle(fontSize: 32.whs, color: Colors.black, fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  " /${controller.episodeList[index].targetLsTimes.toString()}-LS",
                                  style: TextStyle(fontSize: 16.whs, color: Colors.grey, fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            //每一遍LS学习的时长 最近七次
                            Container(
                              // color: Colors.grey,
                              width: 50.whs,
                              height: 50.whs,
                              child: BarChart(
                                BarChartData(
                                  alignment: BarChartAlignment.spaceBetween,
                                  maxY: 10,
                                  barGroups: [
                                    BarChartGroupData(
                                      x: 0,
                                      barRods: [
                                        BarChartRodData(toY: 8, color: const Color(0xFFE3E3E3)),
                                      ],
                                    ),
                                    BarChartGroupData(
                                      x: 1,
                                      barRods: [
                                        BarChartRodData(toY: 10, color: const Color(0xFFE3E3E3)),
                                      ],
                                    ),
                                    BarChartGroupData(
                                      x: 2,
                                      barRods: [
                                        BarChartRodData(toY: 6, color: const Color(0xFFE3E3E3)),
                                      ],
                                    ),
                                    BarChartGroupData(
                                      x: 3,
                                      barRods: [
                                        BarChartRodData(toY: 2, color: const Color(0xFFE3E3E3)),
                                      ],
                                    ),
                                  ],
                                  titlesData: const FlTitlesData(show: false),
                                  borderData: FlBorderData(show: false),
                                  gridData: const FlGridData(show: false),
                                ),
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
